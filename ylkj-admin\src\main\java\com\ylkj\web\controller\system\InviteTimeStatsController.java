package com.ylkj.web.controller.system;

import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.system.model.dto.inviteStats.InviteTimeStatsDto;
import com.ylkj.system.service.IInviteTimeStatsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 邀请码时间维度统计Controller
 * 
 * <AUTHOR>
 * @date 2025-01-23
 */
@RestController
@RequestMapping("/system/invite/time-stats")
public class InviteTimeStatsController extends BaseController {
    
    @Autowired
    private IInviteTimeStatsService inviteTimeStatsService;
    
    /**
     * 获取每日统计数据
     */
    @PreAuthorize("@ss.hasPermi('system:invite:stats')")
    @GetMapping("/daily")
    public AjaxResult getDailyStats(@RequestParam(required = false) String startDate,
                                   @RequestParam(required = false) String endDate,
                                   @RequestParam(required = false) String inviteCode) {
        List<InviteTimeStatsDto> stats = inviteTimeStatsService.getDailyStats(startDate, endDate, inviteCode);
        return success(stats);
    }
    
    /**
     * 获取每周统计数据
     */
    @PreAuthorize("@ss.hasPermi('system:invite:stats')")
    @GetMapping("/weekly")
    public AjaxResult getWeeklyStats(@RequestParam(required = false) String startDate,
                                    @RequestParam(required = false) String endDate,
                                    @RequestParam(required = false) String inviteCode) {
        List<InviteTimeStatsDto> stats = inviteTimeStatsService.getWeeklyStats(startDate, endDate, inviteCode);
        return success(stats);
    }
    
    /**
     * 获取每月统计数据
     */
    @PreAuthorize("@ss.hasPermi('system:invite:stats')")
    @GetMapping("/monthly")
    public AjaxResult getMonthlyStats(@RequestParam(required = false) String startDate,
                                     @RequestParam(required = false) String endDate,
                                     @RequestParam(required = false) String inviteCode) {
        List<InviteTimeStatsDto> stats = inviteTimeStatsService.getMonthlyStats(startDate, endDate, inviteCode);
        return success(stats);
    }
}
