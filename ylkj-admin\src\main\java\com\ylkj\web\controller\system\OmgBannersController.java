package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgBanners;
import com.ylkj.system.model.vo.omgBanners.OmgBannersVo;
import com.ylkj.system.model.dto.omgBanners.OmgBannersQuery;
import com.ylkj.system.model.dto.omgBanners.OmgBannersInsert;
import com.ylkj.system.model.dto.omgBanners.OmgBannersEdit;
import com.ylkj.system.service.IOmgBannersService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 商品轮播图Controller
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@RestController
@RequestMapping("/system/banners")
public class OmgBannersController extends BaseController
{
    @Resource
    private IOmgBannersService omgBannersService;

    /**
     * 查询商品轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('system:banners:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgBannersQuery omgBannersQuery)
    {
        OmgBanners omgBanners = OmgBannersQuery.queryToObj(omgBannersQuery);
        startPage();
        List<OmgBanners> list = omgBannersService.selectOmgBannersList(omgBanners);
        List<OmgBannersVo> listVo= list.stream().map(OmgBannersVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出商品轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('system:banners:export')")
    @Log(title = "商品轮播图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgBannersQuery omgBannersQuery)
    {
        OmgBanners omgBanners = OmgBannersQuery.queryToObj(omgBannersQuery);
        List<OmgBanners> list = omgBannersService.selectOmgBannersList(omgBanners);
        ExcelUtil<OmgBanners> util = new ExcelUtil<OmgBanners>(OmgBanners.class);
        util.exportExcel(response, list, "商品轮播图数据");
    }

    /**
     * 获取商品轮播图详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:banners:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgBanners omgBanners = omgBannersService.selectOmgBannersById(id);
        return success(OmgBannersVo.objToVo(omgBanners));
    }

    /**
     * 新增商品轮播图
     */
    @PreAuthorize("@ss.hasPermi('system:banners:add')")
    @Log(title = "商品轮播图", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgBannersInsert omgBannersInsert)
    {
        OmgBanners omgBanners = OmgBannersInsert.insertToObj(omgBannersInsert);
        return toAjax(omgBannersService.insertOmgBanners(omgBanners));
    }

    /**
     * 修改商品轮播图
     */
    @PreAuthorize("@ss.hasPermi('system:banners:edit')")
    @Log(title = "商品轮播图", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgBannersEdit omgBannersEdit)
    {
        OmgBanners omgBanners = OmgBannersEdit.editToObj(omgBannersEdit);
        return toAjax(omgBannersService.updateOmgBanners(omgBanners));
    }

    /**
     * 删除商品轮播图
     */
    @PreAuthorize("@ss.hasPermi('system:banners:remove')")
    @Log(title = "商品轮播图", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgBannersService.deleteOmgBannersByIds(ids));
    }
}
