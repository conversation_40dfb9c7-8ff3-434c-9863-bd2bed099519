package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgHomeBanners;
import com.ylkj.system.model.vo.omgHomeBanners.OmgHomeBannersVo;
import com.ylkj.system.model.dto.omgHomeBanners.OmgHomeBannersQuery;
import com.ylkj.system.model.dto.omgHomeBanners.OmgHomeBannersInsert;
import com.ylkj.system.model.dto.omgHomeBanners.OmgHomeBannersEdit;
import com.ylkj.system.service.IOmgHomeBannersService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * omg首页轮播图Controller
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@RestController
@RequestMapping("/system/homeBanners")
public class OmgHomeBannersController extends BaseController
{
    @Resource
    private IOmgHomeBannersService omgHomeBannersService;

    /**
     * 查询omg首页轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('system:homeBanners:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgHomeBannersQuery omgHomeBannersQuery)
    {
        OmgHomeBanners omgHomeBanners = OmgHomeBannersQuery.queryToObj(omgHomeBannersQuery);
        startPage();
        List<OmgHomeBanners> list = omgHomeBannersService.selectOmgHomeBannersList(omgHomeBanners);
        List<OmgHomeBannersVo> listVo= list.stream().map(OmgHomeBannersVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出omg首页轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('system:homeBanners:export')")
    @Log(title = "omg首页轮播图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgHomeBannersQuery omgHomeBannersQuery)
    {
        OmgHomeBanners omgHomeBanners = OmgHomeBannersQuery.queryToObj(omgHomeBannersQuery);
        List<OmgHomeBanners> list = omgHomeBannersService.selectOmgHomeBannersList(omgHomeBanners);
        ExcelUtil<OmgHomeBanners> util = new ExcelUtil<OmgHomeBanners>(OmgHomeBanners.class);
        util.exportExcel(response, list, "omg首页轮播图数据");
    }

    /**
     * 获取omg首页轮播图详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:homeBanners:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgHomeBanners omgHomeBanners = omgHomeBannersService.selectOmgHomeBannersById(id);
        return success(OmgHomeBannersVo.objToVo(omgHomeBanners));
    }

    /**
     * 新增omg首页轮播图
     */
    @PreAuthorize("@ss.hasPermi('system:homeBanners:add')")
    @Log(title = "omg首页轮播图", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgHomeBannersInsert omgHomeBannersInsert)
    {
        OmgHomeBanners omgHomeBanners = OmgHomeBannersInsert.insertToObj(omgHomeBannersInsert);
        return toAjax(omgHomeBannersService.insertOmgHomeBanners(omgHomeBanners));
    }

    /**
     * 修改omg首页轮播图
     */
    @PreAuthorize("@ss.hasPermi('system:homeBanners:edit')")
    @Log(title = "omg首页轮播图", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgHomeBannersEdit omgHomeBannersEdit)
    {
        OmgHomeBanners omgHomeBanners = OmgHomeBannersEdit.editToObj(omgHomeBannersEdit);
        return toAjax(omgHomeBannersService.updateOmgHomeBanners(omgHomeBanners));
    }

    /**
     * 删除omg首页轮播图
     */
    @PreAuthorize("@ss.hasPermi('system:homeBanners:remove')")
    @Log(title = "omg首页轮播图", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgHomeBannersService.deleteOmgHomeBannersByIds(ids));
    }
}
