package com.ylkj.system.model.result;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: 吴居乐
 * @Description: 主图处理结果类
 * @DateTime: 2025/8/4/周一 18:16
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MainImageProcessResult {

    private boolean success;
    private String message;
    private String mainImageUrl;

    public static MainImageProcessResult success(String mainImageUrl) {
        return new MainImageProcessResult(true, "处理成功", mainImageUrl);
    }

    public static MainImageProcessResult failure(String message) {
        return new MainImageProcessResult(false, message, null);
    }
}
