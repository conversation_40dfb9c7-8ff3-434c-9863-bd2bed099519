-- 创建商品主图表
CREATE TABLE `omg_product_main_images` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `sku` varchar(100) NOT NULL COMMENT '商品SKU',
  `item_id` varchar(100) DEFAULT NULL COMMENT '商品ID',
  `mall_type` varchar(50) DEFAULT NULL COMMENT '商城类型(T1688,TAOBAO,WEIDIAN等)',
  `original_image_url` text COMMENT '原始图片URL',
  `oss_image_url` varchar(500) NOT NULL COMMENT 'OSS图片URL',
  `image_name` varchar(200) DEFAULT NULL COMMENT '图片文件名',
  `image_size` bigint(20) DEFAULT NULL COMMENT '图片大小(字节)',
  `image_type` varchar(50) DEFAULT NULL COMMENT '图片类型(jpg,png,gif等)',
  `display_order` int(11) DEFAULT '0' COMMENT '显示顺序',
  `is_main` tinyint(1) DEFAULT '0' COMMENT '是否为主图(0:否,1:是)',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态(active:有效,deleted:已删除)',
  `source` varchar(50) DEFAULT 'findqc' COMMENT '图片来源(findqc,cnfans,manual等)',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(64) DEFAULT NULL COMMENT '创建者',
  `update_by` varchar(64) DEFAULT NULL COMMENT '更新者',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sku_oss_url` (`sku`,`oss_image_url`),
  KEY `idx_sku` (`sku`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_mall_type` (`mall_type`),
  KEY `idx_is_main` (`is_main`),
  KEY `idx_status` (`status`),
  KEY `idx_source` (`source`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品主图表';

-- 创建索引优化查询性能
CREATE INDEX `idx_sku_status_order` ON `omg_product_main_images` (`sku`, `status`, `display_order`);
CREATE INDEX `idx_main_image` ON `omg_product_main_images` (`sku`, `is_main`, `status`);
