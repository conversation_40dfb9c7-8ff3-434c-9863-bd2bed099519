package com.ylkj.system.service.impl;

import java.util.*;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import com.ylkj.common.utils.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;

import javax.annotation.Resource;

import com.ylkj.system.mapper.OmgBrandsMapper;
import com.ylkj.system.mapper.OmgProductImagesMapper;
import com.ylkj.system.mapper.SOmgCategoriesMapper;
import com.ylkj.system.model.domain.OmgBrands;
import com.ylkj.system.model.domain.OmgProductImages;
import com.ylkj.system.model.domain.SOmgCategories;
import com.ylkj.system.model.vo.agtProducts.UpdateStatusVo;
import com.ylkj.system.model.vo.omgProducts.ImportResultVo;
import com.ylkj.common.utils.ImportPerformanceMonitor;
import com.ylkj.system.service.IAsyncMainImageUpdateService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylkj.system.mapper.OmgProductsMapper;
import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.service.IOmgProductsService;
import com.ylkj.system.model.dto.omgProducts.OmgProductsQuery;
import com.ylkj.system.model.vo.omgProducts.OmgProductsVo;

/**
 * omg_商品Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
public class OmgProductsServiceImpl extends ServiceImpl<OmgProductsMapper, OmgProducts> implements IOmgProductsService {
    @Resource
    private OmgProductsMapper omgProductsMapper;

    @Resource
    private OmgProductImagesMapper omgProductImagesMapper;

    @Resource
    private SOmgCategoriesMapper  sOmgCategoriesMapper;

    @Resource
    private OmgBrandsMapper  omgBrandsMapper;

    @Resource
    private ImportCacheService importCacheService;

    @Resource
    private IAsyncMainImageUpdateService asyncMainImageUpdateService;

    /**
     * 查询omg_商品
     *
     * @param productId omg_商品主键
     * @return omg_商品
     */
    @Override
    public OmgProducts selectOmgProductsByProductId(Long productId) {
        OmgProducts omgProducts = omgProductsMapper.selectOmgProductsByProductId(productId);
        if (StringUtils.isNotNull(omgProducts)) {
            List<OmgProductImages> omgProductImages = omgProductImagesMapper.selectQcImagesByProductId(productId);
            if (StringUtils.isNotEmpty(omgProductImages)){
                omgProducts.setQcImages(omgProductImages);
            }
        }
        return omgProducts;
    }

    /**
     * 查询omg_商品列表
     *
     * @param omgProducts omg_商品
     * @return omg_商品
     */
    @Override
    public List<OmgProducts> selectOmgProductsList(OmgProducts omgProducts) {
        return omgProductsMapper.selectOmgProductsList(omgProducts);
    }

    /**
     * 新增omg_商品
     *
     * @param omgProducts omg_商品
     * @return 结果
     */
    @Override
    public int insertOmgProducts(OmgProducts omgProducts) {
        int res = omgProductsMapper.insertOmgProducts(omgProducts);
        if (omgProducts.getBrandId() != null) {
            omgProductsMapper.insertOmgProductRelativeBrand(omgProducts);
        }
        if ( omgProducts.getQcImages() != null){
             omgProducts.getQcImages().forEach(qcImages -> {
                qcImages.setProductId(omgProducts.getProductId());
                qcImages.setImageUrl(qcImages.getImageUrl());
                omgProductImagesMapper.insertQcImages(qcImages);
            });
        }
        return res;
    }

    /**
     * 修改omg_商品
     *
     * @param omgProducts omg_商品
     * @return 结果
     */
    @Override
    public int updateOmgProducts(OmgProducts omgProducts) {
        //获取商品下的qc图片，先进行删除后再新增图片
        omgProductImagesMapper.deleteQcImagesByProductId(omgProducts.getProductId());

        if (omgProducts.getQcImages()!= null) {
            omgProducts.getQcImages().forEach(qcImages -> {
                qcImages.setProductId(omgProducts.getProductId());
                qcImages.setImageUrl(qcImages.getImageUrl());
                omgProductImagesMapper.insertQcImages(qcImages);
            });
        }

        if (omgProducts.getBrandId() != null) {
            omgProductsMapper.deleteOmgProductRelativeBrandByProductId(omgProducts.getProductId());
            omgProductsMapper.insertOmgProductRelativeBrand(omgProducts);
        }
        return omgProductsMapper.updateOmgProducts(omgProducts);
    }

    /**
     * 批量删除omg_商品
     *
     * @param productIds 需要删除的omg_商品主键
     * @return 结果
     */
    @Override
    public int deleteOmgProductsByProductIds(Long[] productIds) {
        return omgProductsMapper.deleteOmgProductsByProductIds(productIds);
    }

    /**
     * 删除omg_商品信息
     *
     * @param productId omg_商品主键
     * @return 结果
     */
    @Override
    public int deleteOmgProductsByProductId(Long productId) {
        return omgProductsMapper.deleteOmgProductsByProductId(productId);
    }

    //endregion
    @Override
    public QueryWrapper<OmgProducts> getQueryWrapper(OmgProductsQuery omgProductsQuery) {
        QueryWrapper<OmgProducts> queryWrapper = new QueryWrapper<>();
        //如果不使用params可以删除
        Map<String, Object> params = omgProductsQuery.getParams();
        if (StringUtils.isNull(params)) {
            params = new HashMap<>();
        }
        String name = omgProductsQuery.getName();
        queryWrapper.like(StringUtils.isNotEmpty(name), "name", name);

        String slug = omgProductsQuery.getSlug();
        queryWrapper.like(StringUtils.isNotEmpty(slug), "slug", slug);

        BigDecimal price = omgProductsQuery.getPrice();
        queryWrapper.ge("price", price);

        String status = omgProductsQuery.getStatus();
        queryWrapper.eq(StringUtils.isNotEmpty(status), "status", status);

        return queryWrapper;
    }

    @Override
    public List<OmgProductsVo> convertVoList(List<OmgProducts> omgProductsList) {
        if (StringUtils.isEmpty(omgProductsList)) {
            return Collections.emptyList();
        }
        return omgProductsList.stream().map(OmgProductsVo::objToVo).collect(Collectors.toList());
    }


    /**
     * 修改商品状态
     *
     * @param vo
     * @return
     */
    @Override
    public int updateOmgProductsStatus(UpdateStatusVo vo) {
        int omgProducts = omgProductsMapper.updateOmgProductsStatus(vo);
        if (omgProducts == 0) {
            return 0;
        }
        return omgProducts;
    }

    @Override
    public ImportResultVo importOmgProducts(List<OmgProducts> omgProductsList) {
        ImportResultVo result = new ImportResultVo();

        if (omgProductsList == null || omgProductsList.isEmpty()) {
            result.setMessage("没有要导入的商品数据");
            return result;
        }

        result.setTotalCount(omgProductsList.size());

        // 用于收集成功导入的商品
        List<OmgProducts> successfulProducts = new ArrayList<>();

        for (OmgProducts product : omgProductsList) {
            try {
                // 检查SKU是否已存在，如果存在则跳过导入
                if (product.getSku() != null && !product.getSku().trim().isEmpty()) {
                    if (isSkuExists(product.getSku())) {
                        result.incrementSkipped();
                        result.addSkippedSku(product.getSku());
                        System.out.println("商品SKU: " + product.getSku() + " 已存在，跳过导入");
                        continue; // 跳过当前商品，继续处理下一个
                    }
                }
                //处理商品分类
                if (product.getCategoryName() != null){
                    //根据分类名称查找id
                    Long categoryId = sOmgCategoriesMapper.selectCategoryIdByName(product.getCategoryName());
                    System.err.println("categoryId"+categoryId);
                    //若id为空，则插入
                    if (categoryId == null){
                        SOmgCategories sOmgCategories = new SOmgCategories();
                        sOmgCategories.setName(product.getCategoryName());
                        System.err.println(sOmgCategories);
                        sOmgCategoriesMapper.insertOmgCategories(sOmgCategories);
                        product.setCategoryId(sOmgCategories.getCategoryId());
                    }else {
                        //否则插入id
                        product.setCategoryId(categoryId);
                    }
                }

                //处理二级分类
                if (product.getSecondCategoryName() != null){
                    //根据二级分类名称查找id
                    Long brandId = omgBrandsMapper.selectBrandIdByName(product.getSecondCategoryName(), product.getCategoryId());
                    System.err.println(brandId);
                    if (brandId == null){
                        OmgBrands omgBrands = new OmgBrands();
                        omgBrands.setName(product.getSecondCategoryName());
                        omgBrands.setParentCategoryId(product.getCategoryId());
                        omgBrandsMapper.insertOmgBrands(omgBrands);
                        product.setBrandId(omgBrands.getBrandId());
                    }else {
                        //否则插入id
                        product.setBrandId(brandId);
                    }
                    System.err.println(product);
                }
                product.setPrice(product.getPrice().divide(new BigDecimal("1"), 2, RoundingMode.HALF_UP));
                //处理原价
                product.setOriginalPrice(product.getOriginalPrice().divide(new BigDecimal("1"), 2, RoundingMode.HALF_UP));
                //将产品插入数据库
                int i = omgProductsMapper.insertOmgProducts(product);

                if (i > 0) {
                    result.incrementSuccess();
                    int r = omgProductsMapper.insertOmgProductRelativeBrand(product);
                    // 收集成功导入的商品
                    successfulProducts.add(product);
                } else {
                    result.incrementFailed();
                    result.addFailedSku(product.getSku());
                }

            } catch (Exception e) {
                result.incrementFailed();
                result.addFailedSku(product.getSku());
                System.err.println("导入商品时发生异常，SKU: " + (product.getSku() != null ? product.getSku() : "未知") + "，异常信息: " + e.getMessage());
                e.printStackTrace();
            }
        }

        // 生成导入结果消息
        result.generateMessage();

        // 设置成功导入的商品列表
        result.setSuccessfulProducts(successfulProducts);

        // 输出导入结果统计
        System.out.println("=== 商品导入结果统计 ===");
        System.out.println("成功导入: " + result.getSuccessCount() + " 个商品");
        System.out.println("跳过重复: " + result.getSkippedCount() + " 个商品");
        System.out.println("导入失败: " + result.getFailedCount() + " 个商品");
        System.out.println("总计处理: " + result.getTotalCount() + " 个商品");

        // 如果有成功导入的商品，异步更新主图
        if (result.getSuccessCount() > 0 && !successfulProducts.isEmpty()) {
            try {
                log.info("开始异步更新 {} 个成功导入商品的主图", successfulProducts.size());
                asyncMainImageUpdateService.asyncBatchUpdateMainImages(successfulProducts);
            } catch (Exception e) {
                log.error("启动异步主图更新失败", e);
                // 不影响导入结果，只记录错误
            }
        }

        return result;
    }

    /**
     * 更新淘宝商品链接
     * 将平台为淘宝且sku不为空的商品链接更新为：https://item.taobao.com/item.htm?id=sku
     * @return 更新成功的记录数
     */
    @Override
    public int updateTaobaoProductsUrl() {
        return omgProductsMapper.updateTaobaoProductsUrl();
    }

    /**
     * 更新1688商品链接
     * 将平台为1688且sku不为空的商品链接更新为：https://detail.1688.com/offer/sku.html
     * @return 更新成功的记录数
     */
    @Override
    public int update1688ProductsUrl() {
        return omgProductsMapper.update1688ProductsUrl();
    }

    /**
     * 更新微店商品链接
     * 将平台为微店且sku不为空的商品链接更新为：https://weidian.com/item.html?itemID=sku
     * @return 更新成功的记录数
     */
    @Override
    public int updateWeidianProductsUrl() {
        return omgProductsMapper.updateWeidianProductsUrl();
    }

    /**
     * 根据SKU查询商品
     *
     * @param sku 商品SKU
     * @return 商品信息
     */
    @Override
    public OmgProducts selectOmgProductsBySku(String sku) {
        return omgProductsMapper.selectOmgProductsBySku(sku);
    }

    /**
     * 获取所有商品的SKU列表
     *
     * @return SKU列表
     */
    @Override
    public List<String> getAllProductSkus() {
        return omgProductsMapper.getAllProductSkus();
    }

    /**
     * 检查SKU是否已存在
     *
     * @param sku 商品SKU
     * @return true-存在，false-不存在
     */
    @Override
    public boolean isSkuExists(String sku) {
        if (sku == null || sku.trim().isEmpty()) {
            return false;
        }
        OmgProducts existingProduct = omgProductsMapper.selectOmgProductsBySku(sku.trim());
        return existingProduct != null;
    }

    /**
     * 批量检查SKU是否存在
     *
     * @param skuList SKU列表
     * @return 存在的SKU列表
     */
    @Override
    public List<String> checkSkusExist(List<String> skuList) {
        if (skuList == null || skuList.isEmpty()) {
            return new ArrayList<>();
        }

        // 使用批量查询优化性能
        return omgProductsMapper.batchCheckSkuExists(skuList);
    }

    /**
     * 大数据量商品导入（优化版本）
     *
     * @param omgProductsList 商品列表
     * @param batchSize 批处理大小，建议500-1000
     * @return 导入结果详情
     */
    @Override
    public ImportResultVo importOmgProductsBatch(List<OmgProducts> omgProductsList, int batchSize) {
        ImportResultVo result = new ImportResultVo();

        if (omgProductsList == null || omgProductsList.isEmpty()) {
            result.setMessage("没有要导入的商品数据");
            return result;
        }

        result.setTotalCount(omgProductsList.size());

        // 设置默认批处理大小
        if (batchSize <= 0) {
            batchSize = 500;
        }

        // 开始性能监控
        String taskId = "batch_import_" + System.currentTimeMillis();
        ImportPerformanceMonitor.startMonitor(taskId);

        // 清理缓存，确保数据一致性
        importCacheService.clearAll();

        System.out.println("=== 开始大数据量商品导入 ===");
        System.out.println("总商品数量: " + omgProductsList.size());
        System.out.println("批处理大小: " + batchSize);

        // 用于收集成功导入的商品
        List<OmgProducts> allSuccessfulProducts = new ArrayList<>();

        // 分批处理
        for (int i = 0; i < omgProductsList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, omgProductsList.size());
            List<OmgProducts> batch = omgProductsList.subList(i, endIndex);

            System.out.println("正在处理第 " + (i / batchSize + 1) + " 批，商品数量: " + batch.size());

            // 处理当前批次
            ImportResultVo batchResult = processBatch(batch, i / batchSize + 1);

            // 合并结果
            result.setSuccessCount(result.getSuccessCount() + batchResult.getSuccessCount());
            result.setSkippedCount(result.getSkippedCount() + batchResult.getSkippedCount());
            result.setFailedCount(result.getFailedCount() + batchResult.getFailedCount());
            result.getSkippedSkus().addAll(batchResult.getSkippedSkus());
            result.getFailedSkus().addAll(batchResult.getFailedSkus());

            // 收集成功导入的商品（从批次中筛选出成功的商品）
            if (batchResult.getSuccessCount() > 0) {
                List<OmgProducts> batchSuccessfulProducts = getSuccessfulProductsFromBatch(batch, batchResult);
                allSuccessfulProducts.addAll(batchSuccessfulProducts);
            }
        }

        // 将成功导入的商品列表存储到result中（扩展ImportResultVo或使用其他方式）
        result.setSuccessfulProducts(allSuccessfulProducts);

        // 生成最终结果消息
        result.generateMessage();

        // 结束性能监控
        ImportPerformanceMonitor.endMonitor(taskId, result.getTotalCount(),
            result.getSuccessCount(), result.getSkippedCount(), result.getFailedCount());

        System.out.println("=== 大数据量商品导入完成 ===");
        System.out.println("成功导入: " + result.getSuccessCount() + " 个商品");
        System.out.println("跳过重复: " + result.getSkippedCount() + " 个商品");
        System.out.println("导入失败: " + result.getFailedCount() + " 个商品");
        System.out.println("总计处理: " + result.getTotalCount() + " 个商品");

        // 如果有成功导入的商品，异步更新主图
        if (result.getSuccessCount() > 0 && result.getSuccessfulProducts() != null && !result.getSuccessfulProducts().isEmpty()) {
            try {
                log.info("开始异步更新 {} 个成功导入商品的主图", result.getSuccessfulProducts().size());
                asyncMainImageUpdateService.asyncBatchUpdateMainImages(result.getSuccessfulProducts());
            } catch (Exception e) {
                log.error("启动异步主图更新失败", e);
                // 不影响导入结果，只记录错误
            }
        }

        return result;
    }

    /**
     * 获取所有商品的SKU和平台信息
     *
     * @return 商品列表（包含SKU和平台信息）
     */
    @Override
    public List<OmgProducts> selectAllProductsWithSkuAndPlatform() {
        return omgProductsMapper.selectAllProductsWithSkuAndPlatform();
    }

    /**
     * 根据平台获取商品列表
     *
     * @param platform 平台类型
     * @return 商品列表
     */
    @Override
    public List<OmgProducts> selectProductsByPlatform(String platform) {
        return omgProductsMapper.selectProductsByPlatform(platform);
    }

    /**
     * 处理单个批次的商品导入
     *
     * @param batch 批次商品列表
     * @param batchNumber 批次号
     * @return 批次导入结果
     */
    @Transactional(rollbackFor = Exception.class)
    private ImportResultVo processBatch(List<OmgProducts> batch, int batchNumber) {
        ImportResultVo batchResult = new ImportResultVo();

        try {
            // 1. 批量SKU重复检查
            List<String> skuList = batch.stream()
                    .map(OmgProducts::getSku)
                    .filter(sku -> sku != null && !sku.trim().isEmpty())
                    .distinct()
                    .collect(Collectors.toList());

            Set<String> existingSkus = new HashSet<>();
            if (!skuList.isEmpty()) {
                try {
                    List<String> existingSkuList = omgProductsMapper.batchCheckSkuExists(skuList);
                    if (existingSkuList != null) {
                        existingSkus = new HashSet<>(existingSkuList);
                    }
                } catch (Exception e) {
                    System.err.println("批量检查SKU失败，回退到逐个检查: " + e.getMessage());
                    // 回退到逐个检查
                    for (String sku : skuList) {
                        if (isSkuExists(sku)) {
                            existingSkus.add(sku);
                        }
                    }
                }
            }

            // 2. 分离新商品和重复商品
            List<OmgProducts> newProducts = new ArrayList<>();
            for (OmgProducts product : batch) {
                if (product.getSku() != null && !product.getSku().trim().isEmpty()) {
                    if (existingSkus.contains(product.getSku())) {
                        batchResult.incrementSkipped();
                        batchResult.addSkippedSku(product.getSku());
                        continue;
                    }
                }
                newProducts.add(product);
            }

            if (newProducts.isEmpty()) {
                System.out.println("批次 " + batchNumber + " 中所有商品都已存在，跳过处理");
                return batchResult;
            }

            // 3. 批量处理分类和品牌
            processCategoriesAndBrands(newProducts);

            // 4. 批量插入商品
            int insertedCount = omgProductsMapper.batchInsertOmgProducts(newProducts);

            // 5. 批量插入品牌关联
            List<OmgProducts> productsWithBrand = newProducts.stream()
                    .filter(p -> p.getBrandId() != null)
                    .collect(Collectors.toList());

            if (!productsWithBrand.isEmpty()) {
                omgProductsMapper.batchInsertOmgProductRelativeBrand(productsWithBrand);
            }

            batchResult.setSuccessCount(insertedCount);

            System.out.println("批次 " + batchNumber + " 处理完成，成功导入: " + insertedCount + " 个商品");

        } catch (Exception e) {
            System.err.println("批次 " + batchNumber + " 处理失败: " + e.getMessage());
            e.printStackTrace();

            // 将整个批次标记为失败
            for (OmgProducts product : batch) {
                batchResult.incrementFailed();
                batchResult.addFailedSku(product.getSku());
            }
        }

        return batchResult;
    }

    /**
     * 批量处理分类和品牌信息
     *
     * @param products 商品列表
     */
    private void processCategoriesAndBrands(List<OmgProducts> products) {
        // 缓存已处理的分类和品牌，避免重复查询
        Map<String, Long> categoryCache = new HashMap<>();
        Map<String, Long> brandCache = new HashMap<>();

        for (OmgProducts product : products) {
            try {
                // 处理商品分类
                if (product.getCategoryName() != null) {
                    Long categoryId = categoryCache.get(product.getCategoryName());
                    if (categoryId == null) {
                        categoryId = sOmgCategoriesMapper.selectCategoryIdByName(product.getCategoryName());
                        if (categoryId == null) {
                            SOmgCategories category = new SOmgCategories();
                            category.setName(product.getCategoryName());
                            sOmgCategoriesMapper.insertOmgCategories(category);
                            categoryId = category.getCategoryId();
                        }
                        categoryCache.put(product.getCategoryName(), categoryId);
                    }
                    product.setCategoryId(categoryId);
                }

                // 处理二级分类（品牌）
                if (product.getSecondCategoryName() != null && product.getCategoryId() != null) {
                    String brandKey = product.getSecondCategoryName() + "_" + product.getCategoryId();
                    Long brandId = brandCache.get(brandKey);
                    if (brandId == null) {
                        brandId = omgBrandsMapper.selectBrandIdByName(product.getSecondCategoryName(), product.getCategoryId());
                        if (brandId == null) {
                            OmgBrands brand = new OmgBrands();
                            brand.setName(product.getSecondCategoryName());
                            brand.setParentCategoryId(product.getCategoryId());
                            omgBrandsMapper.insertOmgBrands(brand);
                            brandId = brand.getBrandId();
                        }
                        brandCache.put(brandKey, brandId);
                    }
                    product.setBrandId(brandId);
                }

                // 处理价格
                if (product.getPrice() != null) {
                    product.setPrice(product.getPrice().divide(new BigDecimal("1"), 2, RoundingMode.HALF_UP));
                }
                if (product.getOriginalPrice() != null) {
                    product.setOriginalPrice(product.getOriginalPrice().divide(new BigDecimal("1"), 2, RoundingMode.HALF_UP));
                }

            } catch (Exception e) {
                System.err.println("处理商品分类品牌信息失败，SKU: " + product.getSku() + "，错误: " + e.getMessage());
            }
        }
    }



    /**
     * 从批次中获取成功导入的商品
     * 根据批次结果和跳过/失败的SKU列表，筛选出成功导入的商品
     *
     * @param batch 批次商品列表
     * @param batchResult 批次处理结果
     * @return 成功导入的商品列表
     */
    private List<OmgProducts> getSuccessfulProductsFromBatch(List<OmgProducts> batch, ImportResultVo batchResult) {
        List<OmgProducts> successfulProducts = new ArrayList<>();

        if (batch == null || batch.isEmpty() || batchResult.getSuccessCount() == 0) {
            return successfulProducts;
        }

        // 获取跳过和失败的SKU集合
        Set<String> skippedSkus = new HashSet<>(batchResult.getSkippedSkus());
        Set<String> failedSkus = new HashSet<>(batchResult.getFailedSkus());

        // 筛选出成功的商品（不在跳过和失败列表中的商品）
        for (OmgProducts product : batch) {
            String sku = product.getSku();
            if (sku != null && !skippedSkus.contains(sku) && !failedSkus.contains(sku)) {
                successfulProducts.add(product);
            }
        }

        log.debug("从批次中筛选出 {} 个成功导入的商品，批次总数: {}, 成功数: {}",
                successfulProducts.size(), batch.size(), batchResult.getSuccessCount());

        return successfulProducts;
    }
}
