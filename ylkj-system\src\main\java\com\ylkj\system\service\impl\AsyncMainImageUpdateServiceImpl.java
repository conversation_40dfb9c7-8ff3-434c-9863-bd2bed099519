package com.ylkj.system.service.impl;

import com.ylkj.system.mapper.OmgProductsMapper;
import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.service.IAsyncMainImageUpdateService;
import com.ylkj.system.service.IMainImageProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 异步主图更新服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
public class AsyncMainImageUpdateServiceImpl implements IAsyncMainImageUpdateService {

    @Resource
    private OmgProductsMapper omgProductsMapper;

    @Resource
    private IMainImageProcessService mainImageProcessService;

    /**
     * 异步批量更新商品主图
     * 在商品导入完成后调用，异步处理主图获取和更新
     *
     * @param successfulProducts 成功导入的商品列表
     */
    @Override
    @Async("taskExecutor")
    public void asyncBatchUpdateMainImages(List<OmgProducts> successfulProducts) {
        if (successfulProducts == null || successfulProducts.isEmpty()) {
            log.info("没有需要更新主图的商品");
            return;
        }

        log.info("开始异步批量更新商品主图，商品数量: {}", successfulProducts.size());
        
        int successCount = 0;
        int failedCount = 0;
        int skippedCount = 0;

        for (OmgProducts product : successfulProducts) {
            try {
                String sku = product.getSku();
                String platform = product.getPlatform();

                // 检查SKU和平台是否有效
                if (sku == null || sku.trim().isEmpty()) {
                    log.warn("商品SKU为空，跳过主图更新，商品ID: {}", product.getProductId());
                    skippedCount++;
                    continue;
                }

                if (platform == null || platform.trim().isEmpty()) {
                    log.warn("商品平台为空，跳过主图更新，SKU: {}", sku);
                    skippedCount++;
                    continue;
                }

                // 获取平台对应的mallType
                String mallType = getMallTypeByPlatform(platform);
                if (mallType == null) {
                    log.warn("平台 {} 不支持主图处理，跳过主图更新，SKU: {}", platform, sku);
                    skippedCount++;
                    continue;
                }

                // 调用主图处理逻辑
                IMainImageProcessService.MainImageProcessResult processResult =
                        mainImageProcessService.processProductMainImage(product, mallType);

                if (processResult.isSuccess() && processResult.getMainImageUrl() != null) {
                    // 更新商品主图
                    product.setMainImage(processResult.getMainImageUrl());
                    int updateResult = omgProductsMapper.updateOmgProducts(product);

                    if (updateResult > 0) {
                        successCount++;
                        log.debug("SKU: {} 主图更新成功，主图URL: {}", sku, processResult.getMainImageUrl());
                    } else {
                        failedCount++;
                        log.warn("SKU: {} 主图更新失败，数据库更新失败", sku);
                    }
                } else {
                    failedCount++;
                    log.warn("SKU: {} 主图获取失败: {}", sku, processResult.getMessage());
                }

                // 添加延迟避免请求过于频繁
                Thread.sleep(1500);

            } catch (Exception e) {
                failedCount++;
                log.error("处理商品主图异常，SKU: {}", product.getSku(), e);
            }
        }

        log.info("异步批量更新商品主图完成，总数: {}, 成功: {}, 失败: {}, 跳过: {}", 
                successfulProducts.size(), successCount, failedCount, skippedCount);
    }

    /**
     * 异步更新单个商品主图
     * 
     * @param product 商品信息
     */
    @Override
    @Async("taskExecutor")
    public void asyncUpdateSingleMainImage(OmgProducts product) {
        if (product == null) {
            log.warn("商品信息为空，跳过主图更新");
            return;
        }

        try {
            String sku = product.getSku();
            String platform = product.getPlatform();

            if (sku == null || sku.trim().isEmpty()) {
                log.warn("商品SKU为空，跳过主图更新，商品ID: {}", product.getProductId());
                return;
            }

            if (platform == null || platform.trim().isEmpty()) {
                log.warn("商品平台为空，跳过主图更新，SKU: {}", sku);
                return;
            }

            String mallType = getMallTypeByPlatform(platform);
            if (mallType == null) {
                log.warn("平台 {} 不支持主图处理，跳过主图更新，SKU: {}", platform, sku);
                return;
            }

            IMainImageProcessService.MainImageProcessResult processResult =
                    mainImageProcessService.processProductMainImage(product, mallType);

            if (processResult.isSuccess() && processResult.getMainImageUrl() != null) {
                product.setMainImage(processResult.getMainImageUrl());
                int updateResult = omgProductsMapper.updateOmgProducts(product);

                if (updateResult > 0) {
                    log.info("SKU: {} 主图更新成功，主图URL: {}", sku, processResult.getMainImageUrl());
                } else {
                    log.warn("SKU: {} 主图更新失败，数据库更新失败", sku);
                }
            } else {
                log.warn("SKU: {} 主图获取失败: {}", sku, processResult.getMessage());
            }

        } catch (Exception e) {
            log.error("处理单个商品主图异常，SKU: {}", product.getSku(), e);
        }
    }

    /**
     * 根据平台名称获取对应的mallType
     * 
     * @param platform 平台名称
     * @return mallType
     */
    @Override
    public String getMallTypeByPlatform(String platform) {
        if (platform == null) {
            return null;
        }
        switch (platform.toLowerCase()) {
            case "淘宝":
            case "taobao":
                return "TAOBAO";
            case "1688":
                return "T1688";
            case "weidian":
            case "微店":
                return "WEIDIAN";
            default:
                return null;
        }
    }


}
