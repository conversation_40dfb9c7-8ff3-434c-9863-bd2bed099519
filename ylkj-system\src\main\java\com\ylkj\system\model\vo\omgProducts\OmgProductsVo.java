package com.ylkj.system.model.vo.omgProducts;

import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ylkj.system.model.domain.OmgBrands;
import com.ylkj.system.model.domain.OmgProductImages;
import lombok.Data;
import com.ylkj.common.annotation.Excel;
import org.springframework.beans.BeanUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ylkj.system.model.domain.OmgProducts;
/**
 * omg_商品Vo对象 omg_products
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
public class OmgProductsVo implements Serializable
{
    private static final long serialVersionUID = 1L;

    /** 商品唯一标识 */
    @TableId(value = "product_id", type = IdType.ASSIGN_ID)
    private Long productId;

    /** 关联卖家ID */
    private Long sellerId;

    /** 分类id */
    private Long categoryId;
    /** 商品名称 */
    @Excel(name = "商品名称")
    private String name;

    /** 商品标识符（用于URL） */
    @Excel(name = "商品标识符", readConverterExp = "用=于URL")
    private String slug;

    /** 商品描述 */
    @Excel(name = "商品描述")
    private String description;

    /** 商品价格 */
    @Excel(name = "商品价格")
    private BigDecimal price;

    /** 商品原价（用于折扣计算） */
    @Excel(name = "商品原价", readConverterExp = "用=于折扣计算")
    private BigDecimal originalPrice;

    /** 商品主图链接 */
    @Excel(name = "商品主图链接")
    private String mainImage;

    /** 商品SKU编号 */
    @Excel(name = "商品SKU编号")
    private String sku;

    /** 商品库存 */
    @Excel(name = "商品库存")
    private Long stock;

    /** 商品点赞数 */
    @Excel(name = "商品点赞数")
    private Long likes;

    /** 商品浏览量 */
    @Excel(name = "商品浏览量")
    private Long views;

    /** 虚拟浏览量 */
    @Excel(name = "虚拟浏览量")
    private Long virtualViews;

    /** 商品评分 */
    @Excel(name = "商品评分")
    private BigDecimal rating;

    /** 评分总数 */
    @Excel(name = "评分总数")
    private Long totalRatings;

    /** 商品状态 */
    @Excel(name = "商品状态")
    private String status;

    /** 商品创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "商品创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createdAt;
    /** 关联品牌信息 */
    private OmgBrands OmgBrand;

    /** 商品图片信息 */
    private List<OmgProductImages> qcImages ;

    /** 商品所属商家 */
    private String merchant;

    /** 商品所属平台 */
    private String platform;
    /**
     * 商品标签
     */
    private String tag;

    /**
     * 原链接
     */
    private String fromUrl;

    /**
     * 商品推荐状态
     */
    private Integer productStatus;

    /**
     * QC数量
     */
    private String qc;

    private String video;

    /**
     * 运输天数
     */
    private String averageArrival;

    /**
     * 商品重量
     */
    private BigDecimal weight;

    /**
     * 商品属性：SP是标品，REP是仿品
     */
    @Excel(name = "商品属性", readConverterExp = "SP=标品,REP=仿品")
    private String productAttributes;

     /**
     * 对象转封装类
     *
     * @param omgProducts OmgProducts实体对象
     * @return OmgProductsVo
     */
    public static OmgProductsVo objToVo(OmgProducts omgProducts) {
        if (omgProducts == null) {
            return null;
        }
        OmgProductsVo omgProductsVo = new OmgProductsVo();
        BeanUtils.copyProperties(omgProducts, omgProductsVo);
        return omgProductsVo;
    }
}
