package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgBrands;
import com.ylkj.system.model.vo.omgBrands.OmgBrandsVo;
import com.ylkj.system.model.dto.omgBrands.OmgBrandsQuery;
import com.ylkj.system.model.dto.omgBrands.OmgBrandsInsert;
import com.ylkj.system.model.dto.omgBrands.OmgBrandsEdit;
import com.ylkj.system.service.IOmgBrandsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * omg_品牌Controller
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RestController
@RequestMapping("/system/OmgBrands")
public class OmgBrandsController extends BaseController {
    @Resource
    private IOmgBrandsService omgBrandsService;

    /**
     * 查询omg_品牌列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgBrands:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgBrandsQuery omgBrandsQuery) {
        OmgBrands omgBrands = OmgBrandsQuery.queryToObj(omgBrandsQuery);
        startPage();
        List<OmgBrands> list = omgBrandsService.selectOmgBrandsList(omgBrands);
        List<OmgBrandsVo> listVo = list.stream().map(OmgBrandsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出omg_品牌列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgBrands:export')")
    @Log(title = "omg_品牌", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgBrandsQuery omgBrandsQuery) {
        OmgBrands omgBrands = OmgBrandsQuery.queryToObj(omgBrandsQuery);
        List<OmgBrands> list = omgBrandsService.selectOmgBrandsList(omgBrands);
        ExcelUtil<OmgBrands> util = new ExcelUtil<OmgBrands>(OmgBrands.class);
        util.exportExcel(response, list, "omg_品牌数据");
    }

    /**
     * 获取omg_品牌详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:OmgBrands:query')")
    @GetMapping(value = "/{brandId}")
    public AjaxResult getInfo(@PathVariable("brandId") Long brandId) {
        OmgBrands omgBrands = omgBrandsService.selectOmgBrandsByBrandId(brandId);
        return success(OmgBrandsVo.objToVo(omgBrands));
    }

    /**
     * 新增omg_品牌
     */
    @PreAuthorize("@ss.hasPermi('system:OmgBrands:add')")
    @Log(title = "omg_品牌", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgBrandsInsert omgBrandsInsert) {
        OmgBrands omgBrands = OmgBrandsInsert.insertToObj(omgBrandsInsert);
        return toAjax(omgBrandsService.insertOmgBrands(omgBrands));
    }

    /**
     * 修改omg_品牌
     */
    @PreAuthorize("@ss.hasPermi('system:OmgBrands:edit')")
    @Log(title = "omg_品牌", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgBrandsEdit omgBrandsEdit) {
        OmgBrands omgBrands = OmgBrandsEdit.editToObj(omgBrandsEdit);
        return toAjax(omgBrandsService.updateOmgBrands(omgBrands));
    }

    /**
     * 删除omg_品牌
     */
    @PreAuthorize("@ss.hasPermi('system:OmgBrands:remove')")
    @Log(title = "omg_品牌", businessType = BusinessType.DELETE)
    @DeleteMapping("/{brandIds}")
    public AjaxResult remove(@PathVariable Long[] brandIds) {
        return toAjax(omgBrandsService.deleteOmgBrandsByBrandIds(brandIds));
    }

    @PreAuthorize("@ss.hasPermi('system:OmgBrands:query')")
    @RequestMapping("/getBrandListByCategoryId")
    public AjaxResult getBrandListByCategoryId(@RequestParam("categoryId") Long categoryId) {
        List<OmgBrands> list = omgBrandsService.getBrandListByCategoryId(categoryId);
        List<OmgBrandsVo> listVo = list.stream().map(OmgBrandsVo::objToVo).collect(Collectors.toList());
        return success(listVo);
    }
}
