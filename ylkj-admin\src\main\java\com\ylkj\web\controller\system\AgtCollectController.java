package com.ylkj.web.controller.system;

import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.system.service.IAgtCollectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @BelongsProject: AGTFIND-backbend
 * @BelongsPackage: com.ylkj.controller
 * @Author: 小林
 * @CreateTime: 2025-04-28 14:29
 * @Description: 用户收藏表控制层
 * @Version: 1.0
 */
@RestController
@RequestMapping("/system/collect")
public class AgtCollectController {

    @Autowired
    private IAgtCollectService collectService;
    /**
     * 获取收藏量最多的三个商品
     * @return
     */
    @GetMapping("getMostCollectedProducts")
    public AjaxResult getMostCollectedProducts() {
        return AjaxResult.success(collectService.getMostCollectedProducts());
    }
}