package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgHomeFactoryBanners;
import com.ylkj.system.model.vo.omgHomeFactoryBanners.OmgHomeFactoryBannersVo;
import com.ylkj.system.model.dto.omgHomeFactoryBanners.OmgHomeFactoryBannersQuery;
import com.ylkj.system.model.dto.omgHomeFactoryBanners.OmgHomeFactoryBannersInsert;
import com.ylkj.system.model.dto.omgHomeFactoryBanners.OmgHomeFactoryBannersEdit;
import com.ylkj.system.service.IOmgHomeFactoryBannersService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * omg首页工厂图片Controller
 *
 * <AUTHOR>
 * @date 2025-06-06
 */
@RestController
@RequestMapping("/system/OmgHomeFactorybanners")
public class OmgHomeFactoryBannersController extends BaseController
{
    @Resource
    private IOmgHomeFactoryBannersService omgHomeFactoryBannersService;

    /**
     * 查询omg首页工厂图片列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomeFactorybanners:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgHomeFactoryBannersQuery omgHomeFactoryBannersQuery)
    {
        OmgHomeFactoryBanners omgHomeFactoryBanners = OmgHomeFactoryBannersQuery.queryToObj(omgHomeFactoryBannersQuery);
        startPage();
        List<OmgHomeFactoryBanners> list = omgHomeFactoryBannersService.selectOmgHomeFactoryBannersList(omgHomeFactoryBanners);
        List<OmgHomeFactoryBannersVo> listVo= list.stream().map(OmgHomeFactoryBannersVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出omg首页工厂图片列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomeFactorybanners:export')")
    @Log(title = "omg首页工厂图片", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgHomeFactoryBannersQuery omgHomeFactoryBannersQuery)
    {
        OmgHomeFactoryBanners omgHomeFactoryBanners = OmgHomeFactoryBannersQuery.queryToObj(omgHomeFactoryBannersQuery);
        List<OmgHomeFactoryBanners> list = omgHomeFactoryBannersService.selectOmgHomeFactoryBannersList(omgHomeFactoryBanners);
        ExcelUtil<OmgHomeFactoryBanners> util = new ExcelUtil<OmgHomeFactoryBanners>(OmgHomeFactoryBanners.class);
        util.exportExcel(response, list, "omg首页工厂图片数据");
    }

    /**
     * 获取omg首页工厂图片详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomeFactorybanners:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgHomeFactoryBanners omgHomeFactoryBanners = omgHomeFactoryBannersService.selectOmgHomeFactoryBannersById(id);
        return success(OmgHomeFactoryBannersVo.objToVo(omgHomeFactoryBanners));
    }

    /**
     * 新增omg首页工厂图片
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomeFactorybanners:add')")
    @Log(title = "omg首页工厂图片", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgHomeFactoryBannersInsert omgHomeFactoryBannersInsert)
    {
        OmgHomeFactoryBanners omgHomeFactoryBanners = OmgHomeFactoryBannersInsert.insertToObj(omgHomeFactoryBannersInsert);
        return toAjax(omgHomeFactoryBannersService.insertOmgHomeFactoryBanners(omgHomeFactoryBanners));
    }

    /**
     * 修改omg首页工厂图片
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomeFactorybanners:edit')")
    @Log(title = "omg首页工厂图片", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgHomeFactoryBannersEdit omgHomeFactoryBannersEdit)
    {
        OmgHomeFactoryBanners omgHomeFactoryBanners = OmgHomeFactoryBannersEdit.editToObj(omgHomeFactoryBannersEdit);
        return toAjax(omgHomeFactoryBannersService.updateOmgHomeFactoryBanners(omgHomeFactoryBanners));
    }

    /**
     * 删除omg首页工厂图片
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomeFactorybanners:remove')")
    @Log(title = "omg首页工厂图片", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgHomeFactoryBannersService.deleteOmgHomeFactoryBannersByIds(ids));
    }
}
