package com.ylkj.common.utils;

import com.ylkj.common.model.ImportStats;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 导入性能监控工具类
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class ImportPerformanceMonitor {
    
    private static final Map<String, Long> startTimes = new ConcurrentHashMap<>();
    private static final Map<String, ImportStats> statsMap = new ConcurrentHashMap<>();
    
    /**
     * 开始监控
     * 
     * @param taskId 任务ID
     */
    public static void startMonitor(String taskId) {
        startTimes.put(taskId, System.currentTimeMillis());
        System.out.println("=== 性能监控开始 [" + taskId + "] ===");
        printMemoryInfo("开始时");
    }
    
    /**
     * 结束监控并生成统计报告
     * 
     * @param taskId 任务ID
     * @param totalRecords 总记录数
     * @param successRecords 成功记录数
     * @param skippedRecords 跳过记录数
     * @param failedRecords 失败记录数
     * @return 统计信息
     */
    public static ImportStats endMonitor(String taskId, int totalRecords, 
                                       int successRecords, int skippedRecords, int failedRecords) {
        Long startTime = startTimes.remove(taskId);
        if (startTime == null) {
            System.err.println("警告: 未找到任务 " + taskId + " 的开始时间");
            return null;
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        ImportStats stats = new ImportStats();
        stats.setTotalTime(totalTime);
        stats.setTotalRecords(totalRecords);
        stats.setSuccessRecords(successRecords);
        stats.setSkippedRecords(skippedRecords);
        stats.setFailedRecords(failedRecords);
        
        // 计算处理速度
        if (totalTime > 0) {
            stats.setRecordsPerSecond((double) totalRecords * 1000 / totalTime);
        }
        
        // 获取内存使用情况
        Runtime runtime = Runtime.getRuntime();
        stats.setMemoryUsed(runtime.totalMemory() - runtime.freeMemory());
        
        statsMap.put(taskId, stats);
        
        // 打印性能报告
        printPerformanceReport(taskId, stats);
        
        return stats;
    }
    
    /**
     * 打印性能报告
     * 
     * @param taskId 任务ID
     * @param stats 统计信息
     */
    private static void printPerformanceReport(String taskId, ImportStats stats) {
        System.out.println("\n=== 性能监控报告 [" + taskId + "] ===");
        System.out.println("总耗时: " + formatTime(stats.getTotalTime()));
        System.out.println("总记录数: " + stats.getTotalRecords());
        System.out.println("成功记录: " + stats.getSuccessRecords());
        System.out.println("跳过记录: " + stats.getSkippedRecords());
        System.out.println("失败记录: " + stats.getFailedRecords());
        System.out.printf("处理速度: %.2f 记录/秒%n", stats.getRecordsPerSecond());
        System.out.println("内存使用: " + formatMemory(stats.getMemoryUsed()));
        
        // 性能评估
        if (stats.getRecordsPerSecond() > 100) {
            System.out.println("性能评估: 优秀 ✓");
        } else if (stats.getRecordsPerSecond() > 50) {
            System.out.println("性能评估: 良好 ○");
        } else {
            System.out.println("性能评估: 需要优化 ✗");
            System.out.println("建议: 考虑增加批处理大小或优化数据库连接池配置");
        }
        
        printMemoryInfo("结束时");
        System.out.println("=== 性能监控结束 ===\n");
    }
    
    /**
     * 打印内存信息
     * 
     * @param stage 阶段描述
     */
    public static void printMemoryInfo(String stage) {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;
        long maxMemory = runtime.maxMemory();
        
        System.out.println("内存信息 (" + stage + "):");
        System.out.println("  已用内存: " + formatMemory(usedMemory));
        System.out.println("  可用内存: " + formatMemory(freeMemory));
        System.out.println("  总内存: " + formatMemory(totalMemory));
        System.out.println("  最大内存: " + formatMemory(maxMemory));
        System.out.printf("  内存使用率: %.2f%%%n", (double) usedMemory / totalMemory * 100);
    }
    
    /**
     * 格式化时间显示
     * 
     * @param milliseconds 毫秒
     * @return 格式化的时间字符串
     */
    private static String formatTime(long milliseconds) {
        if (milliseconds < 1000) {
            return milliseconds + "ms";
        } else if (milliseconds < 60000) {
            return String.format("%.2fs", milliseconds / 1000.0);
        } else {
            long minutes = milliseconds / 60000;
            long seconds = (milliseconds % 60000) / 1000;
            return minutes + "m" + seconds + "s";
        }
    }
    
    /**
     * 格式化内存显示
     * 
     * @param bytes 字节数
     * @return 格式化的内存字符串
     */
    private static String formatMemory(long bytes) {
        if (bytes < 1024) {
            return bytes + "B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2fKB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2fMB", bytes / 1024.0 / 1024.0);
        } else {
            return String.format("%.2fGB", bytes / 1024.0 / 1024.0 / 1024.0);
        }
    }
    
    /**
     * 获取统计信息
     * 
     * @param taskId 任务ID
     * @return 统计信息
     */
    public static ImportStats getStats(String taskId) {
        return statsMap.get(taskId);
    }
    
    /**
     * 清理统计信息
     * 
     * @param taskId 任务ID
     */
    public static void clearStats(String taskId) {
        statsMap.remove(taskId);
        startTimes.remove(taskId);
    }
    
    /**
     * 清理所有统计信息
     */
    public static void clearAllStats() {
        statsMap.clear();
        startTimes.clear();
    }
}
