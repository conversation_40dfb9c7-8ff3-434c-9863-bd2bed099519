package com.ylkj.web.controller.system;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.AgtUsers;
import com.ylkj.system.model.vo.agtUsers.AgtUsersVo;
import com.ylkj.system.model.dto.agtUsers.AgtUsersQuery;
import com.ylkj.system.model.dto.agtUsers.AgtUsersInsert;
import com.ylkj.system.model.dto.agtUsers.AgtUsersEdit;
import com.ylkj.system.service.IAgtUsersService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 用户信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-20
 */
@RestController
@RequestMapping("/system/users")
public class AgtUsersController extends BaseController
{
    @Resource
    private IAgtUsersService agtUsersService;

    /**
     * 查询用户信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:users:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgtUsersQuery agtUsersQuery)
    {
        AgtUsers agtUsers = AgtUsersQuery.queryToObj(agtUsersQuery);
        startPage();
        List<AgtUsers> list = agtUsersService.selectAgtUsersList(agtUsers);
        List<AgtUsersVo> listVo= list.stream().map(AgtUsersVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出用户信息列表
     */
    @PreAuthorize("@ss.hasPermi('system:users:export')")
    @Log(title = "用户信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtUsersQuery agtUsersQuery)
    {
        AgtUsers agtUsers = AgtUsersQuery.queryToObj(agtUsersQuery);
        List<AgtUsers> list = agtUsersService.selectAgtUsersList(agtUsers);
        ExcelUtil<AgtUsers> util = new ExcelUtil<AgtUsers>(AgtUsers.class);
        util.exportExcel(response, list, "用户信息数据");
    }

    /**
     * 获取用户信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:users:query')")
    @GetMapping(value = "/{userId}")
    public AjaxResult getInfo(@PathVariable("userId") Long userId)
    {
        AgtUsers agtUsers = agtUsersService.selectAgtUsersByUserId(userId);
        return success(AgtUsersVo.objToVo(agtUsers));
    }

    /**
     * 新增用户信息
     */
    @PreAuthorize("@ss.hasPermi('system:users:add')")
    @Log(title = "用户信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgtUsersInsert agtUsersInsert)
    {
        AgtUsers agtUsers = AgtUsersInsert.insertToObj(agtUsersInsert);
        return toAjax(agtUsersService.insertAgtUsers(agtUsers));
    }

    /**
     * 修改用户信息
     */
    @PreAuthorize("@ss.hasPermi('system:users:edit')")
    @Log(title = "用户信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgtUsersEdit agtUsersEdit)
    {
        AgtUsers agtUsers = AgtUsersEdit.editToObj(agtUsersEdit);
        return toAjax(agtUsersService.updateAgtUsers(agtUsers));
    }

    /**
     * 删除用户信息
     */
    @PreAuthorize("@ss.hasPermi('system:users:remove')")
    @Log(title = "用户信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        return toAjax(agtUsersService.deleteAgtUsersByUserIds(userIds));
    }


    /**
     * @Author: 小林
     * @Description: 统计过去7天的活跃用户总数
     * @DateTime: 2025/4/28/周一
     * @Params: 无
     *
     */
//    @PreAuthorize("@ss.hasPermi('system:users:count')")
    @RequestMapping("countActiveUsersDaily")
    public AjaxResult countActiveUsersDaily() {
        int count = agtUsersService.countActiveUsersDaily();
        return AjaxResult.success(count);
    }

    /**
     * @Author: 小林
     * @Description: 统计过去30天的活跃用户总数
     * @DateTime: 2025/4/28/周一
     * @Params: 无
     *
     */
//    @PreAuthorize("@ss.hasPermi('system:users:count')")
    @RequestMapping("countActiveUsersWeekly")
    public AjaxResult countActiveUsersWeekly() {
        int count = agtUsersService.countActiveUsersWeekly();
        return AjaxResult.success(count);
    }

    /**
     * @Author: 小林
     * @Description: 统计过去90天的活跃用户总数
     * @DateTime: 2025/4/28/周一
     * @Params: 无
     *
     */
//    @PreAuthorize("@ss.hasPermi('system:users:count')")
    @RequestMapping("countActiveUsersMonthly")
    public AjaxResult countActiveUsersMonthly() {
        int count = agtUsersService.countActiveUsersMonthly();
        return AjaxResult.success(count);
    }

    /**
     * @Author: 小林
     * @Description: 统计当天新增用户数
     * @DateTime: 2025/4/28/周一
     * @Params: 无
     *
     */
//    @PreAuthorize("@ss.hasPermi('system:users:count')")
    @RequestMapping("countDailyNewUsers")
    public AjaxResult countDailyNewUsers() {
        List<Map<String, Object>> result = agtUsersService.countDailyNewUsers();
        return AjaxResult.success(result);
    }


    /**
     * @Author: 小林
     * @Description: 统计过去一周每天的新增用户数
     * @DateTime: 2025/4/28/周一
     * @Params: 无
     *
     */
//    @PreAuthorize("@ss.hasPermi('system:users:count')")
    @RequestMapping("countWeeklyNewUsers")
    public AjaxResult countWeeklyNewUsers() {
        List<Map<String, Object>> result = agtUsersService.countWeeklyNewUsers();
        return AjaxResult.success(result);
    }

    /**
     * @Author: 小林
     * @Description: 统计当月每天的新增用户数
     * @DateTime: 2025/4/28/周一
     * @Params: 无
     *
     */
//    @PreAuthorize("@ss.hasPermi('system:users:count')")
    @RequestMapping("countMonthlyNewUsers")
    public AjaxResult countMonthlyNewUsers() {
        List<Map<String, Object>> result = agtUsersService.countMonthlyNewUsers();
        return AjaxResult.success(result);
    }


    /**
     * @Author: 小林
     * @Description: 统计用户总人数
     * @DateTime: 2025/4/28/周二
     * @Params: 无
     *
     */
//    @PreAuthorize("@ss.hasPermi('system:users:count')")
    @RequestMapping("countUserAll")
    public AjaxResult countUserAll() {
        int count = agtUsersService.countUserAll();
        return AjaxResult.success(count);
    }




}
