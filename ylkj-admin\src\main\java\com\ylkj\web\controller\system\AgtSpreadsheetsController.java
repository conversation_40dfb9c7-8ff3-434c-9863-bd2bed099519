package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.AgtSpreadsheets;
import com.ylkj.system.model.vo.agtSpreadsheets.AgtSpreadsheetsVo;
import com.ylkj.system.model.dto.agtSpreadsheets.AgtSpreadsheetsQuery;
import com.ylkj.system.model.dto.agtSpreadsheets.AgtSpreadsheetsInsert;
import com.ylkj.system.model.dto.agtSpreadsheets.AgtSpreadsheetsEdit;
import com.ylkj.system.service.IAgtSpreadsheetsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * agt电子表格Controller
 *
 * <AUTHOR>
 * @date 2025-06-04
 */
@RestController
@RequestMapping("/system/spreadsheets")
public class AgtSpreadsheetsController extends BaseController
{
    @Resource
    private IAgtSpreadsheetsService agtSpreadsheetsService;

    /**
     * 查询agt电子表格列表
     */
    @PreAuthorize("@ss.hasPermi('system:spreadsheets:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgtSpreadsheetsQuery agtSpreadsheetsQuery)
    {
        AgtSpreadsheets agtSpreadsheets = AgtSpreadsheetsQuery.queryToObj(agtSpreadsheetsQuery);
        startPage();
        List<AgtSpreadsheets> list = agtSpreadsheetsService.selectAgtSpreadsheetsList(agtSpreadsheets);
        List<AgtSpreadsheetsVo> listVo= list.stream().map(AgtSpreadsheetsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出agt电子表格列表
     */
    @PreAuthorize("@ss.hasPermi('system:spreadsheets:export')")
    @Log(title = "agt电子表格", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtSpreadsheetsQuery agtSpreadsheetsQuery)
    {
        AgtSpreadsheets agtSpreadsheets = AgtSpreadsheetsQuery.queryToObj(agtSpreadsheetsQuery);
        List<AgtSpreadsheets> list = agtSpreadsheetsService.selectAgtSpreadsheetsList(agtSpreadsheets);
        ExcelUtil<AgtSpreadsheets> util = new ExcelUtil<AgtSpreadsheets>(AgtSpreadsheets.class);
        util.exportExcel(response, list, "agt电子表格数据");
    }

    /**
     * 获取agt电子表格详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:spreadsheets:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        AgtSpreadsheets agtSpreadsheets = agtSpreadsheetsService.selectAgtSpreadsheetsById(id);
        return success(AgtSpreadsheetsVo.objToVo(agtSpreadsheets));
    }

    /**
     * 新增agt电子表格
     */
    @PreAuthorize("@ss.hasPermi('system:spreadsheets:add')")
    @Log(title = "agt电子表格", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgtSpreadsheetsInsert agtSpreadsheetsInsert)
    {
        AgtSpreadsheets agtSpreadsheets = AgtSpreadsheetsInsert.insertToObj(agtSpreadsheetsInsert);
        return toAjax(agtSpreadsheetsService.insertAgtSpreadsheets(agtSpreadsheets));
    }

    /**
     * 修改agt电子表格
     */
    @PreAuthorize("@ss.hasPermi('system:spreadsheets:edit')")
    @Log(title = "agt电子表格", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgtSpreadsheetsEdit agtSpreadsheetsEdit)
    {
        AgtSpreadsheets agtSpreadsheets = AgtSpreadsheetsEdit.editToObj(agtSpreadsheetsEdit);
        return toAjax(agtSpreadsheetsService.updateAgtSpreadsheets(agtSpreadsheets));
    }

    /**
     * 删除agt电子表格
     */
    @PreAuthorize("@ss.hasPermi('system:spreadsheets:remove')")
    @Log(title = "agt电子表格", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(agtSpreadsheetsService.deleteAgtSpreadsheetsByIds(ids));
    }
}
