package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ylkj.system.model.domain.AgtProductDisplay;
import com.ylkj.system.model.vo.agtProducts.UpdateStatusVo;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.AgtProducts;
import com.ylkj.system.model.vo.agtProducts.AgtProductsVo;
import com.ylkj.system.model.dto.agtProducts.AgtProductsQuery;
import com.ylkj.system.model.dto.agtProducts.AgtProductsInsert;
import com.ylkj.system.model.dto.agtProducts.AgtProductsEdit;
import com.ylkj.system.service.IAgtProductsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 商品管理Controller
 *
 * <AUTHOR>
 * @date 2025-04-24
 */
@RestController
@RequestMapping("/system/products")
public class AgtProductsController extends BaseController
{
    @Resource
    private IAgtProductsService agtProductsService;

    /**
     * 查询商品管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:products:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgtProductsQuery agtProductsQuery)
    {
        AgtProducts agtProducts = AgtProductsQuery.queryToObj(agtProductsQuery);
        startPage();
        List<AgtProducts> list = agtProductsService.selectAgtProductsList(agtProducts);
        List<AgtProductsVo> listVo= list.stream().map(AgtProductsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出商品管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:products:export')")
    @Log(title = "商品管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtProductsQuery agtProductsQuery)
    {
        AgtProducts agtProducts = AgtProductsQuery.queryToObj(agtProductsQuery);
        List<AgtProducts> list = agtProductsService.selectAgtProductsList(agtProducts);
        ExcelUtil<AgtProducts> util = new ExcelUtil<AgtProducts>(AgtProducts.class);
        util.exportExcel(response, list, "商品管理数据");
    }

    /**
     * 获取商品管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:products:query')")
    @GetMapping(value = "/{productId}")
    public AjaxResult getInfo(@PathVariable("productId") Long productId)
    {
        AgtProducts agtProducts = agtProductsService.selectAgtProductsByProductId(productId);
        return success(AgtProductsVo.objToVo(agtProducts));
    }

    /**
     * 新增商品管理
     */
    @PreAuthorize("@ss.hasPermi('system:products:add')")
    @Log(title = "商品管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgtProductsInsert agtProductsInsert)
    {
        AgtProducts agtProducts = AgtProductsInsert.insertToObj(agtProductsInsert);
        return toAjax(agtProductsService.insertAgtProducts(agtProducts));
    }

    /**
     * 修改商品管理
     */
    @PreAuthorize("@ss.hasPermi('system:products:edit')")
    @Log(title = "商品管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgtProductsEdit agtProductsEdit)
    {
        AgtProducts agtProducts = AgtProductsEdit.editToObj(agtProductsEdit);
        return toAjax(agtProductsService.updateAgtProducts(agtProducts));
    }

    /**
     * 删除商品管理
     */
    @PreAuthorize("@ss.hasPermi('system:products:remove')")
    @Log(title = "商品管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{productIds}")
    public AjaxResult remove(@PathVariable Long[] productIds)
    {
        return toAjax(agtProductsService.deleteAgtProductsByProductIds(productIds));
    }

    /**
     * 修改商品状态
     */
    @PreAuthorize("@ss.hasPermi('system:products:edit')")
    @Log(title = "商品管理", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult updateProductStatus(@RequestBody UpdateStatusVo vo) {
        return toAjax(agtProductsService.updateAgtProductsStatus(vo));
    }

    /**
     * @Author: 小林
     * @Description: 统计商品总数量
     * @DateTime: 2025/4/28/周一
     * @Params: 无
     * @Return cn.dev33.satoken.util.SaResult
     */
//    @PreAuthorize("@ss.hasPermi('system:products:count')")
    @RequestMapping("countProducts")
    public AjaxResult countProducts() {
        int count = agtProductsService.countProducts();
        return AjaxResult.success(count);
    }


    /**
     * @author: 小许
     * @date: 2025/6/3/周二 18:50
     * @description: 通过商家id查询商品列表
     * @param sellerId
     * @return AjaxResult
     */
    @GetMapping("/seller/{sellerId}")
    public AjaxResult getSellerProducts(@PathVariable("sellerId") Long sellerId) {
        List<AgtProducts> list = agtProductsService.getSellerProducts(sellerId);
        return AjaxResult.success(list);
    }


    /**
     * @author: 小许
     * @date: 2025/6/3/周二 18:50
     * @description: 通过商家id查询展示十个商品列表
     * @param sellerId
     * @return AjaxResult
     */
    @GetMapping("/seller/{sellerId}/display")
    public AjaxResult getSellerProductsDisplay(@PathVariable("sellerId") Long sellerId) {

        return AjaxResult.success(agtProductsService.selectAgtProductDisplayBySellerId(sellerId));
    }


    /**
     * @author: 小许
     * @date: 2025/6/3/周二 18:50
     * @description: 添加商家商品展示
     * @param agtProductDisplay
     * @return AjaxResult
     */
    @PostMapping("/seller/display")
    public AjaxResult addProductDisplay(@RequestBody List<AgtProductDisplay> agtProductDisplay) {

        return AjaxResult.success(agtProductsService.addProductDisplay(agtProductDisplay));
    }

}
