-- ----------------------------
-- LoveGoBuy QC信息表
-- ----------------------------
CREATE TABLE `lovegobuy_qc_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` varchar(100) NOT NULL COMMENT '商品ID',
  `shop_type` varchar(50) NOT NULL COMMENT '商城类型',
  `sku_id` varchar(200) DEFAULT NULL COMMENT 'SKU ID',
  `goods_price` decimal(10,2) DEFAULT NULL COMMENT '商品价格',
  `qc_create_time` varchar(50) DEFAULT NULL COMMENT 'QC创建时间',
  `original_data` longtext DEFAULT NULL COMMENT '原始数据JSON',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_shop_type` (`shop_type`),
  KEY `idx_sku_id` (`sku_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='LoveGoBuy QC信息表';

-- ----------------------------
-- LoveGoBuy SKU信息表
-- ----------------------------
CREATE TABLE `lovegobuy_sku_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `lovegobuy_qc_info_id` bigint(20) NOT NULL COMMENT 'LoveGoBuy QC信息ID',
  `group_name` varchar(100) DEFAULT NULL COMMENT '属性组名称',
  `group_name_cn` varchar(100) DEFAULT NULL COMMENT '属性组中文名称',
  `value_name` varchar(100) DEFAULT NULL COMMENT '属性值名称',
  `value_name_cn` varchar(100) DEFAULT NULL COMMENT '属性值中文名称',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_lovegobuy_qc_info_id` (`lovegobuy_qc_info_id`),
  KEY `idx_group_name` (`group_name`),
  KEY `idx_value_name` (`value_name`),
  CONSTRAINT `fk_lovegobuy_sku_info_qc_id` FOREIGN KEY (`lovegobuy_qc_info_id`) REFERENCES `lovegobuy_qc_info` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='LoveGoBuy SKU信息表';

-- ----------------------------
-- LoveGoBuy QC图片表
-- ----------------------------
CREATE TABLE `lovegobuy_qc_image` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `lovegobuy_qc_info_id` bigint(20) NOT NULL COMMENT 'LoveGoBuy QC信息ID',
  `image_url` varchar(500) NOT NULL COMMENT '图片URL',
  `display_order` int(11) DEFAULT 1 COMMENT '显示顺序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_lovegobuy_qc_info_id` (`lovegobuy_qc_info_id`),
  KEY `idx_display_order` (`display_order`),
  CONSTRAINT `fk_lovegobuy_qc_image_qc_id` FOREIGN KEY (`lovegobuy_qc_info_id`) REFERENCES `lovegobuy_qc_info` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='LoveGoBuy QC图片表'; 