package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ylkj.system.model.domain.OmgInviteVisit;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.vo.omgInviteVisits.OmgInviteVisitsVo;
import com.ylkj.system.model.dto.omgInviteVisits.OmgInviteVisitsQuery;
import com.ylkj.system.model.dto.omgInviteVisits.OmgInviteVisitsInsert;
import com.ylkj.system.model.dto.omgInviteVisits.OmgInviteVisitsEdit;
import com.ylkj.system.service.IOmgInviteVisitsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 邀请码访问记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/system/visits")
public class OmgInviteVisitsController extends BaseController
{
    @Resource
    private IOmgInviteVisitsService omgInviteVisitsService;

    /**
     * 查询邀请码访问记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:visits:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgInviteVisitsQuery omgInviteVisitsQuery)
    {
        OmgInviteVisit omgInviteVisit = OmgInviteVisitsQuery.queryToObj(omgInviteVisitsQuery);
        startPage();
        List<OmgInviteVisit> list = omgInviteVisitsService.selectOmgInviteVisitsList(omgInviteVisit);
        List<OmgInviteVisitsVo> listVo= list.stream().map(OmgInviteVisitsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出邀请码访问记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:visits:export')")
    @Log(title = "邀请码访问记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgInviteVisitsQuery omgInviteVisitsQuery)
    {
        OmgInviteVisit omgInviteVisit = OmgInviteVisitsQuery.queryToObj(omgInviteVisitsQuery);
        List<OmgInviteVisit> list = omgInviteVisitsService.selectOmgInviteVisitsList(omgInviteVisit);
        ExcelUtil<OmgInviteVisit> util = new ExcelUtil<OmgInviteVisit>(OmgInviteVisit.class);
        util.exportExcel(response, list, "邀请码访问记录数据");
    }

    /**
     * 获取邀请码访问记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:visits:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgInviteVisit omgInviteVisit = omgInviteVisitsService.selectOmgInviteVisitsById(id);
        return success(OmgInviteVisitsVo.objToVo(omgInviteVisit));
    }

    /**
     * 新增邀请码访问记录
     */
    @PreAuthorize("@ss.hasPermi('system:visits:add')")
    @Log(title = "邀请码访问记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgInviteVisitsInsert omgInviteVisitsInsert)
    {
        OmgInviteVisit omgInviteVisit = OmgInviteVisitsInsert.insertToObj(omgInviteVisitsInsert);
        return toAjax(omgInviteVisitsService.insertOmgInviteVisits(omgInviteVisit));
    }

    /**
     * 修改邀请码访问记录
     */
    @PreAuthorize("@ss.hasPermi('system:visits:edit')")
    @Log(title = "邀请码访问记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgInviteVisitsEdit omgInviteVisitsEdit)
    {
        OmgInviteVisit omgInviteVisit = OmgInviteVisitsEdit.editToObj(omgInviteVisitsEdit);
        return toAjax(omgInviteVisitsService.updateOmgInviteVisits(omgInviteVisit));
    }

    /**
     * 删除邀请码访问记录
     */
    @PreAuthorize("@ss.hasPermi('system:visits:remove')")
    @Log(title = "邀请码访问记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgInviteVisitsService.deleteOmgInviteVisitsByIds(ids));
    }
}
