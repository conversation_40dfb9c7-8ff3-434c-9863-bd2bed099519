package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgHomePostBanner;
import com.ylkj.system.model.vo.omgHomePostBanner.OmgHomePostBannerVo;
import com.ylkj.system.model.dto.omgHomePostBanner.OmgHomePostBannerQuery;
import com.ylkj.system.model.dto.omgHomePostBanner.OmgHomePostBannerInsert;
import com.ylkj.system.model.dto.omgHomePostBanner.OmgHomePostBannerEdit;
import com.ylkj.system.service.IOmgHomePostBannerService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * omg首页帖子轮播图Controller
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/system/OmgHomePostBanner")
public class OmgHomePostBannerController extends BaseController
{
    @Resource
    private IOmgHomePostBannerService omgHomePostBannerService;

    /**
     * 查询omg首页帖子轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomePostBanner:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgHomePostBannerQuery omgHomePostBannerQuery)
    {
        OmgHomePostBanner omgHomePostBanner = OmgHomePostBannerQuery.queryToObj(omgHomePostBannerQuery);
        startPage();
        List<OmgHomePostBanner> list = omgHomePostBannerService.selectOmgHomePostBannerList(omgHomePostBanner);
        List<OmgHomePostBannerVo> listVo= list.stream().map(OmgHomePostBannerVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出omg首页帖子轮播图列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomePostBanner:export')")
    @Log(title = "omg首页帖子轮播图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgHomePostBannerQuery omgHomePostBannerQuery)
    {
        OmgHomePostBanner omgHomePostBanner = OmgHomePostBannerQuery.queryToObj(omgHomePostBannerQuery);
        List<OmgHomePostBanner> list = omgHomePostBannerService.selectOmgHomePostBannerList(omgHomePostBanner);
        ExcelUtil<OmgHomePostBanner> util = new ExcelUtil<OmgHomePostBanner>(OmgHomePostBanner.class);
        util.exportExcel(response, list, "omg首页帖子轮播图数据");
    }

    /**
     * 获取omg首页帖子轮播图详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomePostBanner:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgHomePostBanner omgHomePostBanner = omgHomePostBannerService.selectOmgHomePostBannerById(id);
        return success(OmgHomePostBannerVo.objToVo(omgHomePostBanner));
    }

    /**
     * 新增omg首页帖子轮播图
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomePostBanner:add')")
    @Log(title = "omg首页帖子轮播图", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgHomePostBannerInsert omgHomePostBannerInsert)
    {
        OmgHomePostBanner omgHomePostBanner = OmgHomePostBannerInsert.insertToObj(omgHomePostBannerInsert);
        return toAjax(omgHomePostBannerService.insertOmgHomePostBanner(omgHomePostBanner));
    }

    /**
     * 修改omg首页帖子轮播图
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomePostBanner:edit')")
    @Log(title = "omg首页帖子轮播图", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgHomePostBannerEdit omgHomePostBannerEdit)
    {
        OmgHomePostBanner omgHomePostBanner = OmgHomePostBannerEdit.editToObj(omgHomePostBannerEdit);
        return toAjax(omgHomePostBannerService.updateOmgHomePostBanner(omgHomePostBanner));
    }

    /**
     * 删除omg首页帖子轮播图
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomePostBanner:remove')")
    @Log(title = "omg首页帖子轮播图", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgHomePostBannerService.deleteOmgHomePostBannerByIds(ids));
    }
}
