package com.ylkj.common.model;

import java.util.ArrayList;
import java.util.List;

/**
 * 验证结果
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class ValidationResult {
    
    /** 是否验证通过 */
    private boolean valid;
    
    /** 错误信息列表 */
    private List<String> errors;
    
    /** 警告信息列表 */
    private List<String> warnings;
    
    /** 验证的字段名称 */
    private String fieldName;
    
    /** 验证的值 */
    private Object value;
    
    public ValidationResult() {
        this.errors = new ArrayList<>();
        this.warnings = new ArrayList<>();
        this.valid = true;
    }
    
    public ValidationResult(String fieldName) {
        this();
        this.fieldName = fieldName;
    }
    
    public ValidationResult(String fieldName, Object value) {
        this(fieldName);
        this.value = value;
    }
    
    /**
     * 添加错误信息
     * 
     * @param error 错误信息
     */
    public void addError(String error) {
        this.errors.add(error);
        this.valid = false;
    }
    
    /**
     * 添加警告信息
     * 
     * @param warning 警告信息
     */
    public void addWarning(String warning) {
        this.warnings.add(warning);
    }
    
    /**
     * 合并其他验证结果
     * 
     * @param other 其他验证结果
     */
    public void merge(ValidationResult other) {
        if (other != null) {
            this.errors.addAll(other.getErrors());
            this.warnings.addAll(other.getWarnings());
            if (!other.isValid()) {
                this.valid = false;
            }
        }
    }
    
    /**
     * 创建错误结果
     * 
     * @param error 错误信息
     * @return 验证结果
     */
    public static ValidationResult error(String error) {
        ValidationResult result = new ValidationResult();
        result.addError(error);
        return result;
    }
    
    /**
     * 创建警告结果
     * 
     * @param warning 警告信息
     * @return 验证结果
     */
    public static ValidationResult warning(String warning) {
        ValidationResult result = new ValidationResult();
        result.addWarning(warning);
        return result;
    }
    
    /**
     * 创建成功结果
     * 
     * @return 验证结果
     */
    public static ValidationResult success() {
        return new ValidationResult();
    }
    
    // Getter and Setter methods
    public boolean isValid() {
        return valid;
    }
    
    public void setValid(boolean valid) {
        this.valid = valid;
    }
    
    public List<String> getErrors() {
        return errors;
    }
    
    public void setErrors(List<String> errors) {
        this.errors = errors;
        // 如果有错误，设置为无效
        if (errors != null && !errors.isEmpty()) {
            this.valid = false;
        }
    }
    
    public List<String> getWarnings() {
        return warnings;
    }
    
    public void setWarnings(List<String> warnings) {
        this.warnings = warnings;
    }
    
    public String getFieldName() {
        return fieldName;
    }
    
    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }
    
    public Object getValue() {
        return value;
    }
    
    public void setValue(Object value) {
        this.value = value;
    }
    
    /**
     * 是否有错误
     * 
     * @return true-有错误，false-无错误
     */
    public boolean hasErrors() {
        return errors != null && !errors.isEmpty();
    }
    
    /**
     * 是否有警告
     * 
     * @return true-有警告，false-无警告
     */
    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }
    
    /**
     * 获取错误数量
     * 
     * @return 错误数量
     */
    public int getErrorCount() {
        return errors != null ? errors.size() : 0;
    }
    
    /**
     * 获取警告数量
     * 
     * @return 警告数量
     */
    public int getWarningCount() {
        return warnings != null ? warnings.size() : 0;
    }
    
    /**
     * 获取第一个错误信息
     * 
     * @return 第一个错误信息，如果没有错误则返回null
     */
    public String getFirstError() {
        return hasErrors() ? errors.get(0) : null;
    }
    
    /**
     * 获取第一个警告信息
     * 
     * @return 第一个警告信息，如果没有警告则返回null
     */
    public String getFirstWarning() {
        return hasWarnings() ? warnings.get(0) : null;
    }
    
    /**
     * 获取所有错误信息的字符串表示
     * 
     * @param separator 分隔符
     * @return 错误信息字符串
     */
    public String getErrorsAsString(String separator) {
        if (!hasErrors()) {
            return "";
        }
        return String.join(separator, errors);
    }
    
    /**
     * 获取所有警告信息的字符串表示
     * 
     * @param separator 分隔符
     * @return 警告信息字符串
     */
    public String getWarningsAsString(String separator) {
        if (!hasWarnings()) {
            return "";
        }
        return String.join(separator, warnings);
    }
    
    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("ValidationResult{");
        sb.append("valid=").append(valid);
        sb.append(", fieldName='").append(fieldName).append('\'');
        sb.append(", value=").append(value);
        
        if (hasErrors()) {
            sb.append(", errors=").append(errors);
        }
        
        if (hasWarnings()) {
            sb.append(", warnings=").append(warnings);
        }
        
        sb.append('}');
        return sb.toString();
    }
}
