package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgSummerNewProducts;
import com.ylkj.system.model.vo.omgSummerNewProducts.OmgSummerNewProductsVo;
import com.ylkj.system.model.dto.omgSummerNewProducts.OmgSummerNewProductsQuery;
import com.ylkj.system.model.dto.omgSummerNewProducts.OmgSummerNewProductsInsert;
import com.ylkj.system.model.dto.omgSummerNewProducts.OmgSummerNewProductsEdit;
import com.ylkj.system.service.IOmgSummerNewProductsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 夏季新品Controller
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/system/OmgSummerProducts")
public class OmgSummerNewProductsController extends BaseController
{
    @Resource
    private IOmgSummerNewProductsService omgSummerNewProductsService;

    /**
     * 查询夏季新品列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgSummerProducts:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgSummerNewProductsQuery omgSummerNewProductsQuery)
    {
        OmgSummerNewProducts omgSummerNewProducts = OmgSummerNewProductsQuery.queryToObj(omgSummerNewProductsQuery);
        startPage();
        List<OmgSummerNewProducts> list = omgSummerNewProductsService.selectOmgSummerNewProductsList(omgSummerNewProducts);
        List<OmgSummerNewProductsVo> listVo= list.stream().map(OmgSummerNewProductsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出夏季新品列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgSummerProducts:export')")
    @Log(title = "夏季新品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgSummerNewProductsQuery omgSummerNewProductsQuery)
    {
        OmgSummerNewProducts omgSummerNewProducts = OmgSummerNewProductsQuery.queryToObj(omgSummerNewProductsQuery);
        List<OmgSummerNewProducts> list = omgSummerNewProductsService.selectOmgSummerNewProductsList(omgSummerNewProducts);
        ExcelUtil<OmgSummerNewProducts> util = new ExcelUtil<OmgSummerNewProducts>(OmgSummerNewProducts.class);
        util.exportExcel(response, list, "夏季新品数据");
    }

    /**
     * 获取夏季新品详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:OmgSummerProducts:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgSummerNewProducts omgSummerNewProducts = omgSummerNewProductsService.selectOmgSummerNewProductsById(id);
        return success(OmgSummerNewProductsVo.objToVo(omgSummerNewProducts));
    }

    /**
     * 新增夏季新品
     */
    @PreAuthorize("@ss.hasPermi('system:OmgSummerProducts:add')")
    @Log(title = "夏季新品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgSummerNewProductsInsert omgSummerNewProductsInsert)
    {
        OmgSummerNewProducts omgSummerNewProducts = OmgSummerNewProductsInsert.insertToObj(omgSummerNewProductsInsert);
        return toAjax(omgSummerNewProductsService.insertOmgSummerNewProducts(omgSummerNewProducts));
    }

    /**
     * 修改夏季新品
     */
    @PreAuthorize("@ss.hasPermi('system:OmgSummerProducts:edit')")
    @Log(title = "夏季新品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgSummerNewProductsEdit omgSummerNewProductsEdit)
    {
        OmgSummerNewProducts omgSummerNewProducts = OmgSummerNewProductsEdit.editToObj(omgSummerNewProductsEdit);
        return toAjax(omgSummerNewProductsService.updateOmgSummerNewProducts(omgSummerNewProducts));
    }

    /**
     * 删除夏季新品
     */
    @PreAuthorize("@ss.hasPermi('system:OmgSummerProducts:remove')")
    @Log(title = "夏季新品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgSummerNewProductsService.deleteOmgSummerNewProductsByIds(ids));
    }
}
