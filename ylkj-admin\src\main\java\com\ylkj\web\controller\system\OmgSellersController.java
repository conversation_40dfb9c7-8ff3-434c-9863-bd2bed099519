package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgSellers;
import com.ylkj.system.model.vo.omgSellers.OmgSellersVo;
import com.ylkj.system.model.dto.omgSellers.OmgSellersQuery;
import com.ylkj.system.model.dto.omgSellers.OmgSellersInsert;
import com.ylkj.system.model.dto.omgSellers.OmgSellersEdit;
import com.ylkj.system.service.IOmgSellersService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * omg_卖家店铺Controller
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@RestController
@RequestMapping("/system/omgSellers")
public class OmgSellersController extends BaseController
{
    @Resource
    private IOmgSellersService omgSellersService;

    /**
     * 查询omg_卖家店铺列表
     */
    @PreAuthorize("@ss.hasPermi('system:omgSellers:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgSellersQuery omgSellersQuery)
    {
        OmgSellers omgSellers = OmgSellersQuery.queryToObj(omgSellersQuery);
        startPage();
        List<OmgSellers> list = omgSellersService.selectOmgSellersList(omgSellers);
        List<OmgSellersVo> listVo= list.stream().map(OmgSellersVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出omg_卖家店铺列表
     */
    @PreAuthorize("@ss.hasPermi('system:omgSellers:export')")
    @Log(title = "omg_卖家店铺", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgSellersQuery omgSellersQuery)
    {
        OmgSellers omgSellers = OmgSellersQuery.queryToObj(omgSellersQuery);
        List<OmgSellers> list = omgSellersService.selectOmgSellersList(omgSellers);
        ExcelUtil<OmgSellers> util = new ExcelUtil<OmgSellers>(OmgSellers.class);
        util.exportExcel(response, list, "omg_卖家店铺数据");
    }

    /**
     * 获取omg_卖家店铺详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:omgSellers:query')")
    @GetMapping(value = "/{sellerId}")
    public AjaxResult getInfo(@PathVariable("sellerId") Long sellerId)
    {
        OmgSellers omgSellers = omgSellersService.selectOmgSellersBySellerId(sellerId);
        return success(OmgSellersVo.objToVo(omgSellers));
    }

    /**
     * 新增omg_卖家店铺
     */
    @PreAuthorize("@ss.hasPermi('system:omgSellers:add')")
    @Log(title = "omg_卖家店铺", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgSellersInsert omgSellersInsert)
    {
        OmgSellers omgSellers = OmgSellersInsert.insertToObj(omgSellersInsert);
        return toAjax(omgSellersService.insertOmgSellers(omgSellers));
    }

    /**
     * 修改omg_卖家店铺
     */
    @PreAuthorize("@ss.hasPermi('system:omgSellers:edit')")
    @Log(title = "omg_卖家店铺", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgSellersEdit omgSellersEdit)
    {
        OmgSellers omgSellers = OmgSellersEdit.editToObj(omgSellersEdit);
        return toAjax(omgSellersService.updateOmgSellers(omgSellers));
    }

    /**
     * 删除omg_卖家店铺
     */
    @PreAuthorize("@ss.hasPermi('system:omgSellers:remove')")
    @Log(title = "omg_卖家店铺", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sellerIds}")
    public AjaxResult remove(@PathVariable Long[] sellerIds)
    {
        return toAjax(omgSellersService.deleteOmgSellersBySellerIds(sellerIds));
    }
}
