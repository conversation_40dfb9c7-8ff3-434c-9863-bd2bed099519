package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgInviteCodes;
import com.ylkj.system.model.vo.omgInviteCodes.OmgInviteCodesVo;
import com.ylkj.system.model.dto.omgInviteCodes.OmgInviteCodesQuery;
import com.ylkj.system.model.dto.omgInviteCodes.OmgInviteCodesInsert;
import com.ylkj.system.model.dto.omgInviteCodes.OmgInviteCodesEdit;
import com.ylkj.system.service.IOmgInviteCodesService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 邀请码Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/system/codes")
public class OmgInviteCodesController extends BaseController
{
    @Resource
    private IOmgInviteCodesService omgInviteCodesService;

    /**
     * 查询邀请码列表
     */
    @PreAuthorize("@ss.hasPermi('system:codes:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgInviteCodesQuery omgInviteCodesQuery)
    {
        OmgInviteCodes omgInviteCodes = OmgInviteCodesQuery.queryToObj(omgInviteCodesQuery);
        startPage();
        List<OmgInviteCodes> list = omgInviteCodesService.selectOmgInviteCodesList(omgInviteCodes);
        List<OmgInviteCodesVo> listVo= list.stream().map(OmgInviteCodesVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出邀请码列表
     */
    @PreAuthorize("@ss.hasPermi('system:codes:export')")
    @Log(title = "邀请码", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgInviteCodesQuery omgInviteCodesQuery)
    {
        OmgInviteCodes omgInviteCodes = OmgInviteCodesQuery.queryToObj(omgInviteCodesQuery);
        List<OmgInviteCodes> list = omgInviteCodesService.selectOmgInviteCodesList(omgInviteCodes);
        ExcelUtil<OmgInviteCodes> util = new ExcelUtil<OmgInviteCodes>(OmgInviteCodes.class);
        util.exportExcel(response, list, "邀请码数据");
    }

    /**
     * 获取邀请码详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:codes:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgInviteCodes omgInviteCodes = omgInviteCodesService.selectOmgInviteCodesById(id);
        return success(OmgInviteCodesVo.objToVo(omgInviteCodes));
    }

    /**
     * 新增邀请码
     */
    @PreAuthorize("@ss.hasPermi('system:codes:add')")
    @Log(title = "邀请码", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgInviteCodesInsert omgInviteCodesInsert)
    {
        OmgInviteCodes omgInviteCodes = OmgInviteCodesInsert.insertToObj(omgInviteCodesInsert);
        return toAjax(omgInviteCodesService.insertOmgInviteCodes(omgInviteCodes));
    }

    /**
     * 修改邀请码
     */
    @PreAuthorize("@ss.hasPermi('system:codes:edit')")
    @Log(title = "邀请码", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgInviteCodesEdit omgInviteCodesEdit)
    {
        OmgInviteCodes omgInviteCodes = OmgInviteCodesEdit.editToObj(omgInviteCodesEdit);
        return toAjax(omgInviteCodesService.updateOmgInviteCodes(omgInviteCodes));
    }

    /**
     * 删除邀请码
     */
    @PreAuthorize("@ss.hasPermi('system:codes:remove')")
    @Log(title = "邀请码", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgInviteCodesService.deleteOmgInviteCodesByIds(ids));
    }
}
