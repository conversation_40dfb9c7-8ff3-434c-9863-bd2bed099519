package com.ylkj.common.utils;

import com.ylkj.common.model.ValidationResult;
import com.ylkj.common.model.BatchValidationResult;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 通用数据验证工具类
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class DataValidator {
    
    // 常用正则表达式
    private static final Pattern SKU_PATTERN = Pattern.compile("^[A-Za-z0-9_-]{1,50}$");
    private static final Pattern EMAIL_PATTERN = Pattern.compile("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$");
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");
    private static final Pattern ID_CARD_PATTERN = Pattern.compile("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$");
    
    // 价格范围
    private static final BigDecimal MIN_PRICE = new BigDecimal("0.01");
    private static final BigDecimal MAX_PRICE = new BigDecimal("999999.99");
    
    /**
     * 验证字符串是否为空
     * 
     * @param value 待验证的值
     * @param fieldName 字段名称
     * @return 验证结果
     */
    public static ValidationResult validateRequired(String value, String fieldName) {
        ValidationResult result = new ValidationResult(fieldName, value);
        
        if (!StringUtils.hasText(value)) {
            result.addError(fieldName + "不能为空");
        }
        
        return result;
    }
    
    /**
     * 验证字符串长度
     * 
     * @param value 待验证的值
     * @param fieldName 字段名称
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return 验证结果
     */
    public static ValidationResult validateLength(String value, String fieldName, int minLength, int maxLength) {
        ValidationResult result = new ValidationResult(fieldName, value);
        
        if (value == null) {
            result.addError(fieldName + "不能为空");
            return result;
        }
        
        int length = value.length();
        
        if (length < minLength) {
            result.addError(fieldName + "长度不能少于" + minLength + "个字符");
        }
        
        if (length > maxLength) {
            result.addError(fieldName + "长度不能超过" + maxLength + "个字符");
        }
        
        // 添加建议性警告
        if (length < minLength + 2 && length >= minLength) {
            result.addWarning(fieldName + "长度较短，建议增加内容");
        }
        
        return result;
    }
    
    /**
     * 验证SKU格式
     * 
     * @param sku SKU值
     * @return 验证结果
     */
    public static ValidationResult validateSku(String sku) {
        ValidationResult result = new ValidationResult("SKU", sku);
        
        if (!StringUtils.hasText(sku)) {
            result.addError("SKU不能为空");
            return result;
        }
        
        if (sku.length() > 50) {
            result.addError("SKU长度不能超过50个字符");
        }
        
        if (!SKU_PATTERN.matcher(sku).matches()) {
            result.addWarning("SKU格式建议使用字母、数字、下划线和连字符");
        }
        
        return result;
    }
    
    /**
     * 验证价格
     * 
     * @param price 价格值
     * @param fieldName 字段名称
     * @return 验证结果
     */
    public static ValidationResult validatePrice(BigDecimal price, String fieldName) {
        ValidationResult result = new ValidationResult(fieldName, price);
        
        if (price == null) {
            result.addError(fieldName + "不能为空");
            return result;
        }
        
        if (price.compareTo(MIN_PRICE) < 0) {
            result.addError(fieldName + "不能小于" + MIN_PRICE);
        }
        
        if (price.compareTo(MAX_PRICE) > 0) {
            result.addError(fieldName + "不能大于" + MAX_PRICE);
        }
        
        // 检查小数位数
        if (price.scale() > 2) {
            result.addWarning(fieldName + "小数位数超过2位，将被四舍五入");
        }
        
        return result;
    }
    
    /**
     * 验证数字范围
     * 
     * @param value 数值
     * @param fieldName 字段名称
     * @param min 最小值
     * @param max 最大值
     * @return 验证结果
     */
    public static ValidationResult validateRange(Integer value, String fieldName, int min, int max) {
        ValidationResult result = new ValidationResult(fieldName, value);
        
        if (value == null) {
            result.addWarning(fieldName + "为空，将使用默认值");
            return result;
        }
        
        if (value < min) {
            result.addError(fieldName + "不能小于" + min);
        }
        
        if (value > max) {
            result.addError(fieldName + "不能大于" + max);
        }
        
        return result;
    }
    
    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱地址
     * @return 验证结果
     */
    public static ValidationResult validateEmail(String email) {
        ValidationResult result = new ValidationResult("邮箱", email);
        
        if (!StringUtils.hasText(email)) {
            result.addError("邮箱不能为空");
            return result;
        }
        
        if (!EMAIL_PATTERN.matcher(email).matches()) {
            result.addError("邮箱格式不正确");
        }
        
        return result;
    }
    
    /**
     * 验证手机号格式
     * 
     * @param phone 手机号
     * @return 验证结果
     */
    public static ValidationResult validatePhone(String phone) {
        ValidationResult result = new ValidationResult("手机号", phone);
        
        if (!StringUtils.hasText(phone)) {
            result.addError("手机号不能为空");
            return result;
        }
        
        if (!PHONE_PATTERN.matcher(phone).matches()) {
            result.addError("手机号格式不正确");
        }
        
        return result;
    }
    
    /**
     * 验证身份证号格式
     * 
     * @param idCard 身份证号
     * @return 验证结果
     */
    public static ValidationResult validateIdCard(String idCard) {
        ValidationResult result = new ValidationResult("身份证号", idCard);
        
        if (!StringUtils.hasText(idCard)) {
            result.addError("身份证号不能为空");
            return result;
        }
        
        if (!ID_CARD_PATTERN.matcher(idCard).matches()) {
            result.addError("身份证号格式不正确");
        }
        
        return result;
    }
    
    /**
     * 验证URL格式
     * 
     * @param url URL地址
     * @param fieldName 字段名称
     * @return 验证结果
     */
    public static ValidationResult validateUrl(String url, String fieldName) {
        ValidationResult result = new ValidationResult(fieldName, url);
        
        if (!StringUtils.hasText(url)) {
            result.addWarning(fieldName + "为空");
            return result;
        }
        
        try {
            new java.net.URL(url);
        } catch (java.net.MalformedURLException e) {
            result.addError(fieldName + "格式不正确");
        }
        
        return result;
    }
    
    /**
     * 验证枚举值
     * 
     * @param value 待验证的值
     * @param fieldName 字段名称
     * @param allowedValues 允许的值列表
     * @return 验证结果
     */
    public static ValidationResult validateEnum(String value, String fieldName, String[] allowedValues) {
        ValidationResult result = new ValidationResult(fieldName, value);
        
        if (!StringUtils.hasText(value)) {
            result.addWarning(fieldName + "为空");
            return result;
        }
        
        boolean isValid = false;
        for (String allowedValue : allowedValues) {
            if (value.equalsIgnoreCase(allowedValue)) {
                isValid = true;
                break;
            }
        }
        
        if (!isValid) {
            result.addError(fieldName + "值不在允许范围内，允许的值：" + String.join(", ", allowedValues));
        }
        
        return result;
    }
    
    /**
     * 批量验证对象列表
     * 
     * @param objects 对象列表
     * @param validator 验证器函数
     * @param <T> 对象类型
     * @return 批量验证结果
     */
    public static <T> BatchValidationResult validateBatch(List<T> objects, 
                                                         java.util.function.Function<T, ValidationResult> validator) {
        BatchValidationResult batchResult = new BatchValidationResult();
        
        if (objects == null || objects.isEmpty()) {
            batchResult.addGlobalError("对象列表为空");
            batchResult.finish();
            return batchResult;
        }
        
        for (int i = 0; i < objects.size(); i++) {
            T object = objects.get(i);
            ValidationResult result = validator.apply(object);
            
            if (result.hasErrors()) {
                batchResult.addInvalidProduct(i, String.valueOf(i), result);
            } else {
                batchResult.addValidProduct();
            }
            
            if (result.hasWarnings()) {
                batchResult.addWarnings(i, String.valueOf(i), result.getWarnings());
            }
        }
        
        batchResult.finish();
        return batchResult;
    }
    
    /**
     * 合并多个验证结果
     * 
     * @param results 验证结果列表
     * @return 合并后的验证结果
     */
    public static ValidationResult mergeResults(ValidationResult... results) {
        ValidationResult merged = new ValidationResult();
        
        for (ValidationResult result : results) {
            if (result != null) {
                merged.merge(result);
            }
        }
        
        return merged;
    }
}
