-- =============================================
-- 脚本名称: 更新omg_products表价格字段乘以7
-- 创建日期: 2025-01-21
-- 描述: 将omg_products表中的价格和原价字段都乘以7
-- =============================================

-- 开始事务，确保数据一致性
BEGIN;

-- 备份当前数据（可选，建议在生产环境执行前先备份）
-- CREATE TABLE omg_products_backup_20250121 AS SELECT * FROM omg_products;

-- 更新价格字段，将价格和原价都乘以7
UPDATE omg_products 
SET 
    price = CASE 
        WHEN price IS NOT NULL AND price > 0 THEN price * 7 
        ELSE price 
    END,
    original_price = CASE 
        WHEN original_price IS NOT NULL AND original_price > 0 THEN original_price * 7 
        ELSE original_price 
    END,
    update_time = NOW()  -- 更新修改时间
WHERE 
    (price IS NOT NULL AND price > 0) 
    OR (original_price IS NOT NULL AND original_price > 0);

-- 显示更新结果统计
SELECT 
    COUNT(*) as total_updated_records,
    MIN(price) as min_price_after_update,
    MAX(price) as max_price_after_update,
    AVG(price) as avg_price_after_update,
    MIN(original_price) as min_original_price_after_update,
    MAX(original_price) as max_original_price_after_update,
    AVG(original_price) as avg_original_price_after_update
FROM omg_products 
WHERE (price IS NOT NULL AND price > 0) OR (original_price IS NOT NULL AND original_price > 0);

-- 提交事务
COMMIT;

-- 验证更新结果（显示前10条记录）
SELECT 
    id,
    product_name,
    price,
    original_price,
    update_time
FROM omg_products 
WHERE (price IS NOT NULL AND price > 0) OR (original_price IS NOT NULL AND original_price > 0)
ORDER BY update_time DESC 
LIMIT 10;
