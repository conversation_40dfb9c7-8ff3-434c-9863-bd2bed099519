package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import com.ylkj.system.model.domain.AgtRecommendSellers;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.AgtSellers;
import com.ylkj.system.model.vo.agtSellers.AgtSellersVo;
import com.ylkj.system.model.dto.agtSellers.AgtSellersQuery;
import com.ylkj.system.model.dto.agtSellers.AgtSellersInsert;
import com.ylkj.system.model.dto.agtSellers.AgtSellersEdit;
import com.ylkj.system.service.IAgtSellersService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 商家管理Controller
 *
 * <AUTHOR>
 * @date 2025-04-23
 */
@RestController
@RequestMapping("/system/sellers")
public class AgtSellersController extends BaseController
{
    @Resource
    private IAgtSellersService agtSellersService;

    /**
     * 查询商家管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:sellers:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgtSellersQuery agtSellersQuery)
    {
        AgtSellers agtSellers = AgtSellersQuery.queryToObj(agtSellersQuery);
        startPage();
        List<AgtSellers> list = agtSellersService.selectAgtSellersList(agtSellers);
        List<AgtSellersVo> listVo= list.stream().map(AgtSellersVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出商家管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:sellers:export')")
    @Log(title = "商家管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtSellersQuery agtSellersQuery)
    {
        AgtSellers agtSellers = AgtSellersQuery.queryToObj(agtSellersQuery);
        List<AgtSellers> list = agtSellersService.selectAgtSellersList(agtSellers);
        ExcelUtil<AgtSellers> util = new ExcelUtil<AgtSellers>(AgtSellers.class);
        util.exportExcel(response, list, "商家管理数据");
    }

    /**
     * 获取商家管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:sellers:query')")
    @GetMapping(value = "/{sellerId}")
    public AjaxResult getInfo(@PathVariable("sellerId") Long sellerId)
    {
        AgtSellers agtSellers = agtSellersService.selectAgtSellersBySellerId(sellerId);
        return success(AgtSellersVo.objToVo(agtSellers));
    }

    /**
     * 新增商家管理
     */
    @PreAuthorize("@ss.hasPermi('system:sellers:add')")
    @Log(title = "商家管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgtSellersInsert agtSellersInsert)
    {
        AgtSellers agtSellers = AgtSellersInsert.insertToObj(agtSellersInsert);
        return toAjax(agtSellersService.insertAgtSellers(agtSellers));
    }

    /**
     * 修改商家管理
     */
    @PreAuthorize("@ss.hasPermi('system:sellers:edit')")
    @Log(title = "商家管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgtSellersEdit agtSellersEdit)
    {
        AgtSellers agtSellers = AgtSellersEdit.editToObj(agtSellersEdit);
        return toAjax(agtSellersService.updateAgtSellers(agtSellers));
    }

    /**
     * 删除商家管理
     */
    @PreAuthorize("@ss.hasPermi('system:sellers:remove')")
    @Log(title = "商家管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{sellerIds}")
    public AjaxResult remove(@PathVariable Long[] sellerIds)
    {
        return toAjax(agtSellersService.deleteAgtSellersBySellerIds(sellerIds));
    }

    /**
     * 统计店铺总数
     */
//    @PreAuthorize("@ss.hasPermi('system:sellers:count')")
    @GetMapping("/countStores")
    public AjaxResult countStores() {
        int count = agtSellersService.countStores();
        return AjaxResult.success(count);
    }


    /**
     * @author: 小许
     * @date: 2025/5/29/周四 14:41
     * @description: 推荐商家
     * @return AjaxResult
     */
    @PostMapping("/recommend")
    public AjaxResult recommendSellerBySellerId(@RequestBody List<AgtRecommendSellers> agtRecommendSellersList) {
        return AjaxResult.success(agtSellersService.recommendSellerBySellerId(agtRecommendSellersList));

    }

    /**
     * @author: 小许
     * @date: 2025/5/29/周四 15:11
     * @description: 获取已经存在的推荐商家列表
     * @return AjaxResult
     */
    @GetMapping("/recommend/list")
    public AjaxResult recommendSellerList() {
        return AjaxResult.success(agtSellersService.recommendSellerList());
    }



}
