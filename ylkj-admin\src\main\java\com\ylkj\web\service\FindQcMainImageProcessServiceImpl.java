package com.ylkj.web.service;

import com.ylkj.model.domain.omg.FindQcImageProcessResult;
import com.ylkj.service.FindQcImageProcessService;
import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.service.IMainImageProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * FindQC主图处理服务实现
 * 集成FindQC图片处理服务，用于异步主图更新
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service("findQcMainImageProcessService")
public class FindQcMainImageProcessServiceImpl implements IMainImageProcessService {

    @Autowired
    private FindQcImageProcessService findQcImageProcessService;

    /**
     * 处理商品主图
     * 
     * @param product 商品信息
     * @param mallType 商城类型
     * @return 处理结果
     */
    @Override
    public MainImageProcessResult processProductMainImage(OmgProducts product, String mallType) {
        try {
            String sku = product.getSku();
            log.info("开始处理FindQC商品主图，SKU: {}, mallType: {}", sku, mallType);
            
            // 调用FindQC图片处理服务
            FindQcImageProcessResult result = findQcImageProcessService.processProductImages(
                    sku, mallType, "USD", "en");

            if (result != null && result.isSuccess() && 
                result.getPicListOssUrls() != null && !result.getPicListOssUrls().isEmpty()) {
                
                // 获取第一张图片作为主图
                String mainImageUrl = result.getPicListOssUrls().get(0);
                log.info("SKU: {} FindQC主图获取成功，主图URL: {}", sku, mainImageUrl);
                
                return MainImageProcessResult.success(mainImageUrl);
                
            } else {
                String errorMessage = result != null ? result.getMessage() : "未知错误";
                log.warn("SKU: {} FindQC主图获取失败: {}", sku, errorMessage);
                
                return MainImageProcessResult.failure("FindQC主图获取失败: " + errorMessage);
            }
            
        } catch (Exception e) {
            log.error("处理FindQC商品主图异常，SKU: {}", product.getSku(), e);
            return MainImageProcessResult.failure("处理异常: " + e.getMessage());
        }
    }
}
