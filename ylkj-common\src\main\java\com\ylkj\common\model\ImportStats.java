package com.ylkj.common.model;

/**
 * 导入统计信息
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class ImportStats {
    
    /** 总耗时（毫秒） */
    private long totalTime;
    
    /** 总记录数 */
    private int totalRecords;
    
    /** 成功记录数 */
    private int successRecords;
    
    /** 跳过记录数 */
    private int skippedRecords;
    
    /** 失败记录数 */
    private int failedRecords;
    
    /** 每秒处理记录数 */
    private double recordsPerSecond;
    
    /** 内存使用量（字节） */
    private long memoryUsed;
    
    /** 任务开始时间 */
    private long startTime;
    
    /** 任务结束时间 */
    private long endTime;
    
    // 构造函数
    public ImportStats() {
    }
    
    public ImportStats(long totalTime, int totalRecords, int successRecords, 
                      int skippedRecords, int failedRecords) {
        this.totalTime = totalTime;
        this.totalRecords = totalRecords;
        this.successRecords = successRecords;
        this.skippedRecords = skippedRecords;
        this.failedRecords = failedRecords;
        
        // 计算处理速度
        if (totalTime > 0) {
            this.recordsPerSecond = (double) totalRecords * 1000 / totalTime;
        }
    }
    
    // Getter and Setter methods
    public long getTotalTime() {
        return totalTime;
    }
    
    public void setTotalTime(long totalTime) {
        this.totalTime = totalTime;
    }
    
    public int getTotalRecords() {
        return totalRecords;
    }
    
    public void setTotalRecords(int totalRecords) {
        this.totalRecords = totalRecords;
    }
    
    public int getSuccessRecords() {
        return successRecords;
    }
    
    public void setSuccessRecords(int successRecords) {
        this.successRecords = successRecords;
    }
    
    public int getSkippedRecords() {
        return skippedRecords;
    }
    
    public void setSkippedRecords(int skippedRecords) {
        this.skippedRecords = skippedRecords;
    }
    
    public int getFailedRecords() {
        return failedRecords;
    }
    
    public void setFailedRecords(int failedRecords) {
        this.failedRecords = failedRecords;
    }
    
    public double getRecordsPerSecond() {
        return recordsPerSecond;
    }
    
    public void setRecordsPerSecond(double recordsPerSecond) {
        this.recordsPerSecond = recordsPerSecond;
    }
    
    public long getMemoryUsed() {
        return memoryUsed;
    }
    
    public void setMemoryUsed(long memoryUsed) {
        this.memoryUsed = memoryUsed;
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }
    
    public long getEndTime() {
        return endTime;
    }
    
    public void setEndTime(long endTime) {
        this.endTime = endTime;
    }
    
    /**
     * 获取任务持续时间
     * 
     * @return 持续时间（毫秒）
     */
    public long getDuration() {
        if (endTime > 0 && startTime > 0) {
            return endTime - startTime;
        }
        return totalTime;
    }
    
    /**
     * 计算成功率
     * 
     * @return 成功率（0-100）
     */
    public double getSuccessRate() {
        if (totalRecords == 0) {
            return 0.0;
        }
        return (double) successRecords / totalRecords * 100;
    }
    
    /**
     * 计算失败率
     * 
     * @return 失败率（0-100）
     */
    public double getFailureRate() {
        if (totalRecords == 0) {
            return 0.0;
        }
        return (double) failedRecords / totalRecords * 100;
    }
    
    /**
     * 计算跳过率
     * 
     * @return 跳过率（0-100）
     */
    public double getSkipRate() {
        if (totalRecords == 0) {
            return 0.0;
        }
        return (double) skippedRecords / totalRecords * 100;
    }
    
    /**
     * 判断是否为高性能
     * 
     * @return true-高性能，false-低性能
     */
    public boolean isHighPerformance() {
        return recordsPerSecond > 100;
    }
    
    /**
     * 获取性能等级
     * 
     * @return 性能等级描述
     */
    public String getPerformanceLevel() {
        if (recordsPerSecond > 100) {
            return "优秀";
        } else if (recordsPerSecond > 50) {
            return "良好";
        } else if (recordsPerSecond > 20) {
            return "一般";
        } else {
            return "需要优化";
        }
    }
    
    /**
     * 格式化内存使用量
     * 
     * @return 格式化的内存字符串
     */
    public String getFormattedMemoryUsed() {
        return formatMemory(memoryUsed);
    }
    
    /**
     * 格式化总耗时
     * 
     * @return 格式化的时间字符串
     */
    public String getFormattedTotalTime() {
        return formatTime(totalTime);
    }
    
    /**
     * 格式化时间显示
     */
    private String formatTime(long milliseconds) {
        if (milliseconds < 1000) {
            return milliseconds + "ms";
        } else if (milliseconds < 60000) {
            return String.format("%.2fs", milliseconds / 1000.0);
        } else {
            long minutes = milliseconds / 60000;
            long seconds = (milliseconds % 60000) / 1000;
            return minutes + "m" + seconds + "s";
        }
    }
    
    /**
     * 格式化内存显示
     */
    private String formatMemory(long bytes) {
        if (bytes < 1024) {
            return bytes + "B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2fKB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.2fMB", bytes / 1024.0 / 1024.0);
        } else {
            return String.format("%.2fGB", bytes / 1024.0 / 1024.0 / 1024.0);
        }
    }
    
    @Override
    public String toString() {
        return String.format(
            "ImportStats{totalTime=%s, totalRecords=%d, successRecords=%d, " +
            "skippedRecords=%d, failedRecords=%d, recordsPerSecond=%.2f, " +
            "memoryUsed=%s, successRate=%.2f%%, performanceLevel=%s}",
            getFormattedTotalTime(), totalRecords, successRecords, skippedRecords, 
            failedRecords, recordsPerSecond, getFormattedMemoryUsed(), 
            getSuccessRate(), getPerformanceLevel()
        );
    }
}
