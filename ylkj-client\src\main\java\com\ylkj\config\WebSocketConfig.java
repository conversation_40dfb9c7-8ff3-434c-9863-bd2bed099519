package com.ylkj.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

/**
 * @version 1.0.0
 * @Project：agtfind-backbend
 * @Package com.ylkj.config
 * @Title: WebSocketConfig
 * @author: 小许
 * @date: 2025/5/27/周二 14:14
 * @description: websocket配置类
 */
@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {
    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(new UserCountWebSocketHandler(), "/ws/online")
                .setAllowedOrigins("*"); // Vue前端跨域时需要
    }
}
