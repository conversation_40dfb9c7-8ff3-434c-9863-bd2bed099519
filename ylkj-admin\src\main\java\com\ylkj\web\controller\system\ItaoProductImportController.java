package com.ylkj.web.controller.system;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.core.page.TableDataInfo;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.system.model.domain.ItaoProductImport;
import com.ylkj.system.service.IItaoProductImportService;

/**
 * 商品导入控制层
 */
@RestController
@RequestMapping("/system/import")
public class ItaoProductImportController extends BaseController {
    @Autowired
    private IItaoProductImportService itaoProductImportService;

    /**
     * 查询商品导入列表
     */
    @PreAuthorize("@ss.hasPermi('system:import:list')")
    @GetMapping("/list")
    public TableDataInfo list(ItaoProductImport itaoProductImport) {
        startPage();
        List<ItaoProductImport> list = itaoProductImportService.selectItaoProductImportList(itaoProductImport);
        return getDataTable(list);
    }

    /**
     * 导出商品导入列表
     */
    @PreAuthorize("@ss.hasPermi('system:import:export')")
    @Log(title = "商品导入", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ItaoProductImport itaoProductImport) {
        List<ItaoProductImport> list = itaoProductImportService.selectItaoProductImportList(itaoProductImport);
        ExcelUtil<ItaoProductImport> util = new ExcelUtil<ItaoProductImport>(ItaoProductImport.class);
        util.exportExcel(response, list, "商品导入数据");
    }

    /**
     * 获取商品导入详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:import:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(itaoProductImportService.selectItaoProductImportById(id));
    }

    /**
     * 新增商品导入
     */
    @PreAuthorize("@ss.hasPermi('system:import:add')")
    @Log(title = "商品导入", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ItaoProductImport itaoProductImport) {
        itaoProductImport.extractSkuFromUrl();
        return toAjax(itaoProductImportService.insertItaoProductImport(itaoProductImport));
    }

    /**
     * 修改商品导入
     */
    @PreAuthorize("@ss.hasPermi('system:import:edit')")
    @Log(title = "商品导入", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ItaoProductImport itaoProductImport) {
        itaoProductImport.extractSkuFromUrl();
        return toAjax(itaoProductImportService.updateItaoProductImport(itaoProductImport));
    }

    /**
     * 删除商品导入
     */
    @PreAuthorize("@ss.hasPermi('system:import:remove')")
    @Log(title = "商品导入", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(itaoProductImportService.deleteItaoProductImportByIds(ids));
    }

    /**
     * 导入数据
     */
//    @PreAuthorize("@ss.hasPermi('system:import:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        // 批处理参数
        int batchSize = 200; // 减小批次大小，进一步降低内存占用
        int successTotal = 0;
        int failureTotal = 0;
        StringBuilder resultMsg = new StringBuilder();
        String operName = getUsername();
        
        try {
            // 创建临时文件存储上传的Excel
            File tempFile = File.createTempFile("import", ".xlsx");
            file.transferTo(tempFile);
            
            // 使用POI直接处理Excel，避免一次性加载所有数据
            Workbook workbook = WorkbookFactory.create(tempFile);
            Sheet sheet = workbook.getSheetAt(0);
            
            // 获取Excel头信息
            Row headerRow = sheet.getRow(0);
            int goodsUrlIndex = -1;
            int photoUrlIndex = -1;
            
            // 查找列索引
            for (int i = 0; i < headerRow.getLastCellNum(); i++) {
                Cell cell = headerRow.getCell(i);
                if (cell != null) {
                    String cellValue = cell.getStringCellValue();
                    if ("商品链接".equals(cellValue)) {
                        goodsUrlIndex = i;
                    } else if ("图片链接".equals(cellValue)) {
                        photoUrlIndex = i;
                    }
                }
            }
            
            if (goodsUrlIndex == -1 || photoUrlIndex == -1) {
                return error("Excel格式不正确，缺少必要的列");
            }
            
            // 逐行处理数据，使用批处理
            int rowCount = sheet.getLastRowNum();
            List<ItaoProductImport> batchList = new ArrayList<>(batchSize);
            
            for (int i = 1; i <= rowCount; i++) {
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }
                
                // 创建实体对象
                ItaoProductImport item = new ItaoProductImport();
                
                // 获取单元格数据
                Cell goodsUrlCell = row.getCell(goodsUrlIndex);
                Cell photoUrlCell = row.getCell(photoUrlIndex);
                
                if (goodsUrlCell != null) {
                    item.setGoodsUrl(goodsUrlCell.getStringCellValue());
                }
                
                if (photoUrlCell != null) {
                    item.setPhotoUrl(photoUrlCell.getStringCellValue());
                }
                
                // 提取SKU
                item.extractSkuFromUrl();
                item.setCreateBy(operName);
                
                // 添加到批处理列表
                batchList.add(item);
                
                // 当达到批处理大小或处理到最后一行时，执行批量插入
                if (batchList.size() >= batchSize || i == rowCount) {
                    try {
                        String batchMessage = itaoProductImportService.importData(batchList, updateSupport, operName);
                        
                        // 解析批次处理结果
                        if (batchMessage.contains("成功导入")) {
                            String numStr = batchMessage.replaceAll("[^0-9]", "");
                            try {
                                successTotal += Integer.parseInt(numStr);
                            } catch (NumberFormatException e) {
                                // 忽略解析错误
                            }
                        } else if (batchMessage.contains("导入失败")) {
                            resultMsg.append(batchMessage).append("<br/>");
                            failureTotal++;
                        }
                    } catch (Exception e) {
                        failureTotal++;
                        resultMsg.append("第").append(i - batchList.size() + 1).append("批处理失败: ").append(e.getMessage()).append("<br/>");
                    }
                    
                    // 清空批处理列表，准备下一批
                    batchList.clear();
                }
            }
            
            workbook.close();
            // 删除临时文件
            tempFile.delete();
            
        } catch (Exception e) {
            return error("导入Excel失败: " + e.getMessage());
        }
        
        // 构建最终响应消息
        String finalMessage;
        if (failureTotal > 0) {
            finalMessage = "处理完成，成功导入 " + successTotal + " 条，失败 " + failureTotal + " 批次。<br/>" + resultMsg.toString();
        } else {
            finalMessage = "成功导入 " + successTotal + " 条商品数据";
        }
        
        return success(finalMessage);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<ItaoProductImport> util = new ExcelUtil<ItaoProductImport>(ItaoProductImport.class);
        util.importTemplateExcel(response, "商品导入数据");
    }
} 