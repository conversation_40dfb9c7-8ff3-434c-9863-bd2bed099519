package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.ClientPointsItems;
import com.ylkj.system.model.vo.clientPointsItems.ClientPointsItemsVo;
import com.ylkj.system.model.dto.clientPointsItems.ClientPointsItemsQuery;
import com.ylkj.system.model.dto.clientPointsItems.ClientPointsItemsInsert;
import com.ylkj.system.model.dto.clientPointsItems.ClientPointsItemsEdit;
import com.ylkj.system.service.IClientPointsItemsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 积分兑换商品Controller
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@RestController
@RequestMapping("/system/items")
public class ClientPointsItemsController extends BaseController
{
    @Resource
    private IClientPointsItemsService clientPointsItemsService;

    /**
     * 查询积分兑换商品列表
     */
    @PreAuthorize("@ss.hasPermi('system:items:list')")
    @GetMapping("/list")
    public TableDataInfo list(ClientPointsItemsQuery clientPointsItemsQuery)
    {
        ClientPointsItems clientPointsItems = ClientPointsItemsQuery.queryToObj(clientPointsItemsQuery);
        startPage();
        List<ClientPointsItems> list = clientPointsItemsService.selectClientPointsItemsList(clientPointsItems);
        List<ClientPointsItemsVo> listVo= list.stream().map(ClientPointsItemsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出积分兑换商品列表
     */
    @PreAuthorize("@ss.hasPermi('system:items:export')")
    @Log(title = "积分兑换商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ClientPointsItemsQuery clientPointsItemsQuery)
    {
        ClientPointsItems clientPointsItems = ClientPointsItemsQuery.queryToObj(clientPointsItemsQuery);
        List<ClientPointsItems> list = clientPointsItemsService.selectClientPointsItemsList(clientPointsItems);
        ExcelUtil<ClientPointsItems> util = new ExcelUtil<ClientPointsItems>(ClientPointsItems.class);
        util.exportExcel(response, list, "积分兑换商品数据");
    }

    /**
     * 获取积分兑换商品详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:items:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        ClientPointsItems clientPointsItems = clientPointsItemsService.selectClientPointsItemsById(id);
        return success(ClientPointsItemsVo.objToVo(clientPointsItems));
    }

    /**
     * 新增积分兑换商品
     */
    @PreAuthorize("@ss.hasPermi('system:items:add')")
    @Log(title = "积分兑换商品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ClientPointsItemsInsert clientPointsItemsInsert)
    {
        ClientPointsItems clientPointsItems = ClientPointsItemsInsert.insertToObj(clientPointsItemsInsert);
        return toAjax(clientPointsItemsService.insertClientPointsItems(clientPointsItems));
    }

    /**
     * 修改积分兑换商品
     */
    @PreAuthorize("@ss.hasPermi('system:items:edit')")
    @Log(title = "积分兑换商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ClientPointsItemsEdit clientPointsItemsEdit)
    {
        ClientPointsItems clientPointsItems = ClientPointsItemsEdit.editToObj(clientPointsItemsEdit);
        return toAjax(clientPointsItemsService.updateClientPointsItems(clientPointsItems));
    }

    /**
     * 删除积分兑换商品
     */
    @PreAuthorize("@ss.hasPermi('system:items:remove')")
    @Log(title = "积分兑换商品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(clientPointsItemsService.deleteClientPointsItemsByIds(ids));
    }
}
