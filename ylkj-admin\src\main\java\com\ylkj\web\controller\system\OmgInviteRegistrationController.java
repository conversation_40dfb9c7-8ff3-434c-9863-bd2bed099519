package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgInviteRegistration;
import com.ylkj.system.model.vo.omgInviteRegistration.OmgInviteRegistrationVo;
import com.ylkj.system.model.dto.omgInviteRegistration.OmgInviteRegistrationQuery;
import com.ylkj.system.model.dto.omgInviteRegistration.OmgInviteRegistrationInsert;
import com.ylkj.system.model.dto.omgInviteRegistration.OmgInviteRegistrationEdit;
import com.ylkj.system.service.IOmgInviteRegistrationService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 邀请码注册记录Controller
 *
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/system/registration")
public class OmgInviteRegistrationController extends BaseController
{
    @Resource
    private IOmgInviteRegistrationService omgInviteRegistrationService;

    /**
     * 查询邀请码注册记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:registration:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgInviteRegistrationQuery omgInviteRegistrationQuery)
    {
        OmgInviteRegistration omgInviteRegistration = OmgInviteRegistrationQuery.queryToObj(omgInviteRegistrationQuery);
        startPage();
        List<OmgInviteRegistration> list = omgInviteRegistrationService.selectOmgInviteRegistrationList(omgInviteRegistration);
        List<OmgInviteRegistrationVo> listVo= list.stream().map(OmgInviteRegistrationVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出邀请码注册记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:registration:export')")
    @Log(title = "邀请码注册记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgInviteRegistrationQuery omgInviteRegistrationQuery)
    {
        OmgInviteRegistration omgInviteRegistration = OmgInviteRegistrationQuery.queryToObj(omgInviteRegistrationQuery);
        List<OmgInviteRegistration> list = omgInviteRegistrationService.selectOmgInviteRegistrationList(omgInviteRegistration);
        ExcelUtil<OmgInviteRegistration> util = new ExcelUtil<OmgInviteRegistration>(OmgInviteRegistration.class);
        util.exportExcel(response, list, "邀请码注册记录数据");
    }

    /**
     * 获取邀请码注册记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:registration:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgInviteRegistration omgInviteRegistration = omgInviteRegistrationService.selectOmgInviteRegistrationById(id);
        return success(OmgInviteRegistrationVo.objToVo(omgInviteRegistration));
    }

    /**
     * 新增邀请码注册记录
     */
    @PreAuthorize("@ss.hasPermi('system:registration:add')")
    @Log(title = "邀请码注册记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgInviteRegistrationInsert omgInviteRegistrationInsert)
    {
        OmgInviteRegistration omgInviteRegistration = OmgInviteRegistrationInsert.insertToObj(omgInviteRegistrationInsert);
        return toAjax(omgInviteRegistrationService.insertOmgInviteRegistration(omgInviteRegistration));
    }

    /**
     * 修改邀请码注册记录
     */
    @PreAuthorize("@ss.hasPermi('system:registration:edit')")
    @Log(title = "邀请码注册记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgInviteRegistrationEdit omgInviteRegistrationEdit)
    {
        OmgInviteRegistration omgInviteRegistration = OmgInviteRegistrationEdit.editToObj(omgInviteRegistrationEdit);
        return toAjax(omgInviteRegistrationService.updateOmgInviteRegistration(omgInviteRegistration));
    }

    /**
     * 删除邀请码注册记录
     */
    @PreAuthorize("@ss.hasPermi('system:registration:remove')")
    @Log(title = "邀请码注册记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgInviteRegistrationService.deleteOmgInviteRegistrationByIds(ids));
    }
}
