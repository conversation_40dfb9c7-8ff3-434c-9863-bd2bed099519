package com.ylkj.web.controller.system;

import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.model.domain.SyncResult;
import com.ylkj.service.ICnfansDataSyncService;
import com.ylkj.web.controller.system.dto.SkuSyncRequest;
import com.ylkj.web.controller.system.dto.SkuStringRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * CNFans数据同步管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/system/cnfans-sync")
@Api(tags = "CNFans数据同步管理")
public class CnfansDataSyncController extends BaseController {

    @Autowired
    private ICnfansDataSyncService cnfansDataSyncService;

    /**
     * 根据SKU字符串同步商品数据（支持单个或多个SKU，逗号分隔）
     */
    @PreAuthorize("@ss.hasPermi('system:cnfans:sync')")
    @Log(title = "CNFans数据SKU同步", businessType = BusinessType.UPDATE)
    @PostMapping("/products/sync-by-skus")
    @ApiOperation("根据SKU字符串同步商品数据（支持单个或多个SKU，逗号分隔）")
    public AjaxResult syncProductsBySkus(
            @ApiParam(value = "SKU请求对象", required = true)
            @RequestBody SkuStringRequest request) {

        try {
            if (request == null || request.getSkus() == null || request.getSkus().trim().isEmpty()) {
                return AjaxResult.error("SKU参数不能为空");
            }

            log.info("开始根据SKU字符串同步商品CNFans数据，SKU: {}", request.getSkus());

            // 业务逻辑交给Service层处理
            SyncResult result = cnfansDataSyncService.syncProductsBySkuString(request.getSkus());

            String message = result.getTotal() == 1 ? "单个SKU同步完成" : "批量SKU同步完成";
            return AjaxResult.success(message)
                    .put("total", result.getTotal())
                    .put("success", result.getSuccess())
                    .put("failed", result.getFailed());

        } catch (IllegalArgumentException e) {
            log.warn("同步商品CNFans数据参数错误: {}", e.getMessage());
            return AjaxResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("根据SKU字符串同步商品CNFans数据异常，SKU: {}",
                    request != null ? request.getSkus() : "null", e);
            return AjaxResult.error("同步商品CNFans数据异常: " + e.getMessage());
        }
    }

    /**
     * 同步所有商品的CNFans数据
     */
    @PreAuthorize("@ss.hasPermi('system:cnfans:sync')")
    @Log(title = "CNFans数据全量同步", businessType = BusinessType.UPDATE)
    @PostMapping("/products/all")
    @ApiOperation("同步所有商品的CNFans数据")
    public AjaxResult syncAllProducts() {
        
        try {
            log.info("开始同步所有商品的CNFans数据");
            
            SyncResult result = cnfansDataSyncService.syncAllProducts();
            
            return AjaxResult.success("全量同步完成")
                    .put("total", result.getTotal())
                    .put("success", result.getSuccess())
                    .put("failed", result.getFailed());
            
        } catch (Exception e) {
            log.error("同步所有商品CNFans数据异常", e);
            return AjaxResult.error("同步所有商品CNFans数据异常: " + e.getMessage());
        }
    }

    /**
     * 获取同步状态统计
     */
    @PreAuthorize("@ss.hasPermi('system:cnfans:view')")
    @GetMapping("/status")
    @ApiOperation("获取CNFans数据同步状态统计")
    public AjaxResult getSyncStatus() {
        
        try {
            // 这里可以添加统计逻辑，比如统计有多少商品已同步、多少未同步等
            // 暂时返回基本信息
            return AjaxResult.success("获取同步状态成功")
                    .put("message", "CNFans数据同步功能正常运行");
            
        } catch (Exception e) {
            log.error("获取CNFans数据同步状态异常", e);
            return AjaxResult.error("获取同步状态异常: " + e.getMessage());
        }
    }
}
