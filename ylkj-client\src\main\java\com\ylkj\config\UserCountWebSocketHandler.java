package com.ylkj.config;

import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

/**
 * @version 1.0.0
 * @Project：agtfind-backbend
 * @Package com.ylkj.config
 * @Title: UserCountWebSocketHandler
 * @author: 小许
 * @date: 2025/5/27/周二 14:16
 * @description: WebSocket处理类
 */
@Component
public class UserCountWebSocketHandler extends TextWebSocketHandler {
    // 线程安全集合保存在线用户Session
    private static final Set<WebSocketSession> sessions = ConcurrentHashMap.newKeySet();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) throws Exception {
        sessions.add(session);
        broadcastUserCount();
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) throws Exception {
        sessions.remove(session);
        broadcastUserCount();
    }

    private void broadcastUserCount() throws IOException {
        //在线人数的基础上增加5到20的区间人数
        int realCount = sessions.size();
        int randomAddition = ThreadLocalRandom.current().nextInt(5, 21); // 生成 [5, 20] 区间的随机数
        int fakeCount = realCount + randomAddition;
        String message = String.valueOf(fakeCount);
        for (WebSocketSession session : sessions) {
            if (session.isOpen()) {
                session.sendMessage(new TextMessage(message));
            }
        }
    }
}
