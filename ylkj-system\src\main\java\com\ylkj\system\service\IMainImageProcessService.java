package com.ylkj.system.service;

import com.ylkj.system.model.domain.OmgProducts;

/**
 * 主图处理服务接口
 * 用于解耦异步主图更新服务与具体的图片处理实现
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface IMainImageProcessService {
    
    /**
     * 处理商品主图
     * 
     * @param product 商品信息
     * @param mallType 商城类型
     * @return 处理结果，包含是否成功和新的主图URL
     */
    MainImageProcessResult processProductMainImage(OmgProducts product, String mallType);
    
    /**
     * 主图处理结果
     */
    class MainImageProcessResult {
        private boolean success;
        private String message;
        private String mainImageUrl;
        
        public MainImageProcessResult() {}
        
        public MainImageProcessResult(boolean success, String message, String mainImageUrl) {
            this.success = success;
            this.message = message;
            this.mainImageUrl = mainImageUrl;
        }
        
        public static MainImageProcessResult success(String mainImageUrl) {
            return new MainImageProcessResult(true, "处理成功", mainImageUrl);
        }
        
        public static MainImageProcessResult failure(String message) {
            return new MainImageProcessResult(false, message, null);
        }
        
        // Getters and Setters
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
        
        public String getMessage() {
            return message;
        }
        
        public void setMessage(String message) {
            this.message = message;
        }
        
        public String getMainImageUrl() {
            return mainImageUrl;
        }
        
        public void setMainImageUrl(String mainImageUrl) {
            this.mainImageUrl = mainImageUrl;
        }
    }
}
