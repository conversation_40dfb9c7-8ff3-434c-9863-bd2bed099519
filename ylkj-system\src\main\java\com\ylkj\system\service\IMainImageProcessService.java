package com.ylkj.system.service;

import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.model.result.MainImageProcessResult;

/**
 * 主图处理服务接口
 * 用于解耦异步主图更新服务与具体的图片处理实现
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
public interface IMainImageProcessService {
    
    /**
     * 处理商品主图
     * 
     * @param product 商品信息
     * @param mallType 商城类型
     * @return 处理结果，包含是否成功和新的主图URL
     */
    MainImageProcessResult processProductMainImage(OmgProducts product, String mallType);
    

}
