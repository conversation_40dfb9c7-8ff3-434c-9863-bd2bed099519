package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgHomeVideo;
import com.ylkj.system.model.vo.omgHomeVideo.OmgHomeVideoVo;
import com.ylkj.system.model.dto.omgHomeVideo.OmgHomeVideoQuery;
import com.ylkj.system.model.dto.omgHomeVideo.OmgHomeVideoInsert;
import com.ylkj.system.model.dto.omgHomeVideo.OmgHomeVideoEdit;
import com.ylkj.system.service.IOmgHomeVideoService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * omg首页视频组Controller
 *
 * <AUTHOR>
 * @date 2025-07-05
 */
@RestController
@RequestMapping("/system/OmgHomeVideo")
public class OmgHomeVideoController extends BaseController
{
    @Resource
    private IOmgHomeVideoService omgHomeVideoService;

    /**
     * 查询omg首页视频组列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomeVideo:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgHomeVideoQuery omgHomeVideoQuery)
    {
        OmgHomeVideo omgHomeVideo = OmgHomeVideoQuery.queryToObj(omgHomeVideoQuery);
        startPage();
        List<OmgHomeVideo> list = omgHomeVideoService.selectOmgHomeVideoList(omgHomeVideo);
        List<OmgHomeVideoVo> listVo= list.stream().map(OmgHomeVideoVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出omg首页视频组列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomeVideo:export')")
    @Log(title = "omg首页视频组", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgHomeVideoQuery omgHomeVideoQuery)
    {
        OmgHomeVideo omgHomeVideo = OmgHomeVideoQuery.queryToObj(omgHomeVideoQuery);
        List<OmgHomeVideo> list = omgHomeVideoService.selectOmgHomeVideoList(omgHomeVideo);
        ExcelUtil<OmgHomeVideo> util = new ExcelUtil<OmgHomeVideo>(OmgHomeVideo.class);
        util.exportExcel(response, list, "omg首页视频组数据");
    }

    /**
     * 获取omg首页视频组详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomeVideo:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgHomeVideo omgHomeVideo = omgHomeVideoService.selectOmgHomeVideoById(id);
        return success(OmgHomeVideoVo.objToVo(omgHomeVideo));
    }

    /**
     * 新增omg首页视频组
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomeVideo:add')")
    @Log(title = "omg首页视频组", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgHomeVideoInsert omgHomeVideoInsert)
    {
        OmgHomeVideo omgHomeVideo = OmgHomeVideoInsert.insertToObj(omgHomeVideoInsert);
        return toAjax(omgHomeVideoService.insertOmgHomeVideo(omgHomeVideo));
    }

    /**
     * 修改omg首页视频组
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomeVideo:edit')")
    @Log(title = "omg首页视频组", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgHomeVideoEdit omgHomeVideoEdit)
    {
        OmgHomeVideo omgHomeVideo = OmgHomeVideoEdit.editToObj(omgHomeVideoEdit);
        return toAjax(omgHomeVideoService.updateOmgHomeVideo(omgHomeVideo));
    }

    /**
     * 删除omg首页视频组
     */
    @PreAuthorize("@ss.hasPermi('system:OmgHomeVideo:remove')")
    @Log(title = "omg首页视频组", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgHomeVideoService.deleteOmgHomeVideoByIds(ids));
    }
}
