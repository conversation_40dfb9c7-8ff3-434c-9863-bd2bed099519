package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.AgtComments;
import com.ylkj.system.model.vo.agtComments.AgtCommentsVo;
import com.ylkj.system.model.dto.agtComments.AgtCommentsQuery;
import com.ylkj.system.model.dto.agtComments.AgtCommentsInsert;
import com.ylkj.system.model.dto.agtComments.AgtCommentsEdit;
import com.ylkj.system.service.IAgtCommentsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 商品评论管理Controller
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@RestController
@RequestMapping("/system/comments")
public class AgtCommentsController extends BaseController
{
    @Resource
    private IAgtCommentsService agtCommentsService;

    /**
     * 查询商品评论管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:comments:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgtCommentsQuery agtCommentsQuery)
    {
        AgtComments agtComments = AgtCommentsQuery.queryToObj(agtCommentsQuery);
        startPage();
        List<AgtComments> list = agtCommentsService.selectAgtCommentsList(agtComments);
        List<AgtCommentsVo> listVo= list.stream().map(AgtCommentsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出商品评论管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:comments:export')")
    @Log(title = "商品评论管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtCommentsQuery agtCommentsQuery)
    {
        AgtComments agtComments = AgtCommentsQuery.queryToObj(agtCommentsQuery);
        List<AgtComments> list = agtCommentsService.selectAgtCommentsList(agtComments);
        ExcelUtil<AgtComments> util = new ExcelUtil<AgtComments>(AgtComments.class);
        util.exportExcel(response, list, "商品评论管理数据");
    }

    /**
     * 获取商品评论管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:comments:query')")
    @GetMapping(value = "/{commentId}")
    public AjaxResult getInfo(@PathVariable("commentId") Long commentId)
    {
        AgtComments agtComments = agtCommentsService.selectAgtCommentsByCommentId(commentId);
        return success(AgtCommentsVo.objToVo(agtComments));
    }

    /**
     * 新增商品评论管理
     */
    @PreAuthorize("@ss.hasPermi('system:comments:add')")
    @Log(title = "商品评论管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgtCommentsInsert agtCommentsInsert)
    {
        AgtComments agtComments = AgtCommentsInsert.insertToObj(agtCommentsInsert);
        return toAjax(agtCommentsService.insertAgtComments(agtComments));
    }

    /**
     * 修改商品评论管理
     */
    @PreAuthorize("@ss.hasPermi('system:comments:edit')")
    @Log(title = "商品评论管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgtCommentsEdit agtCommentsEdit)
    {
        AgtComments agtComments = AgtCommentsEdit.editToObj(agtCommentsEdit);
        return toAjax(agtCommentsService.updateAgtComments(agtComments));
    }

    /**
     * 删除商品评论管理
     */
    @PreAuthorize("@ss.hasPermi('system:comments:remove')")
    @Log(title = "商品评论管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{commentIds}")
    public AjaxResult remove(@PathVariable Long[] commentIds)
    {
        return toAjax(agtCommentsService.deleteAgtCommentsByCommentIds(commentIds));
    }
}
