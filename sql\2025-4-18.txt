CREATE TABLE agt_users (
    user_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '用户唯一标识',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '用户邮箱',
    password VARCHAR(255) NOT NULL COMMENT '用户密码（加密存储）',
    first_name VARCHAR(50) COMMENT '用户名字',
    last_name VARCHAR(50) COMMENT '用户姓氏',
    avatar_url VARCHAR(255) COMMENT '用户头像链接',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '账户创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '账户最后更新时间',
    last_login_at DATETIME COMMENT '用户最后登录时间',
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '用户状态',
    bio TEXT COMMENT '用户个人简介',
    phone_number VARCHAR(20) COMMENT '用户联系电话',
    role ENUM('user', 'admin', 'seller') DEFAULT 'user' COMMENT '用户角色'
) COMMENT='用户信息表';



CREATE TABLE agt_categories (
    category_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '分类唯一标识',
    name VARCHAR(50) NOT NULL COMMENT '分类名称',
    slug VARCHAR(50) NOT NULL UNIQUE COMMENT '分类标识符（用于URL）',
    parent_category_id INT DEFAULT NULL COMMENT '父分类ID（用于层级分类）',
    description TEXT COMMENT '分类描述',
    image_url VARCHAR(255) COMMENT '分类图片链接',
    display_order INT DEFAULT 0 COMMENT '显示顺序',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '分类创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '分类最后更新时间'
) COMMENT='商品分类表';


CREATE TABLE agt_sellers (
    seller_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '卖家唯一标识',
    user_id INT COMMENT '关联用户ID（卖家用户）',
    store_name VARCHAR(100) NOT NULL COMMENT '店铺名称',
    store_slug VARCHAR(100) NOT NULL UNIQUE COMMENT '店铺标识符（用于URL）',
    logo VARCHAR(255) COMMENT '店铺Logo链接',
    cover_image VARCHAR(255) COMMENT '店铺封面图片链接',
    description TEXT COMMENT '店铺描述',
    contact_email VARCHAR(100) COMMENT '店铺联系邮箱',
    contact_phone VARCHAR(20) COMMENT '店铺联系电话',
    address TEXT COMMENT '店铺地址',
    rating DECIMAL(3,2) DEFAULT 0 COMMENT '店铺评分',
    total_ratings INT DEFAULT 0 COMMENT '评分总数',
    verification_status ENUM('unverified', 'pending', 'verified') DEFAULT 'unverified' COMMENT '店铺验证状态',
    joined_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '店铺入驻时间',
    platform_discount_code VARCHAR(50) COMMENT '平台专属折扣码',
    platform_url VARCHAR(255) COMMENT '平台链接',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '店铺状态',
    tier_level ENUM('standard', 'premium', 'elite') DEFAULT 'standard' COMMENT '店铺等级'
) COMMENT='卖家店铺表';


CREATE TABLE agt_products (
    product_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '商品唯一标识',
    seller_id INT NOT NULL COMMENT '关联卖家ID',
    name VARCHAR(255) NOT NULL COMMENT '商品名称',
    slug VARCHAR(255) NOT NULL UNIQUE COMMENT '商品标识符（用于URL）',
    description TEXT COMMENT '商品描述',
    price DECIMAL(10,2) NOT NULL COMMENT '商品价格',
    original_price DECIMAL(10,2) COMMENT '商品原价（用于折扣计算）',
    main_image VARCHAR(255) COMMENT '商品主图链接',
    sku VARCHAR(50) COMMENT '商品SKU编号',
    stock INT DEFAULT 0 COMMENT '商品库存',
    likes INT DEFAULT 0 COMMENT '商品点赞数',
    views INT DEFAULT 0 COMMENT '商品浏览量',
    rating DECIMAL(3,2) DEFAULT 0 COMMENT '商品评分',
    total_ratings INT DEFAULT 0 COMMENT '评分总数',
    status ENUM('active', 'inactive', 'out_of_stock', 'draft') DEFAULT 'active' COMMENT '商品状态',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '商品创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '商品最后更新时间'
) COMMENT='商品表';


CREATE TABLE agt_product_images (
    image_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '图片唯一标识',
    product_id INT NOT NULL COMMENT '关联商品ID',
    image_url VARCHAR(255) NOT NULL COMMENT '图片链接',
    display_order INT DEFAULT 0 COMMENT '显示顺序',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '图片创建时间'
) COMMENT='商品图片表';


CREATE TABLE agt_product_categories (
    product_id INT NOT NULL COMMENT '关联商品ID',
    category_id INT NOT NULL COMMENT '关联分类ID',
    PRIMARY KEY (product_id, category_id)
) COMMENT='商品分类关联表';


CREATE TABLE agt_comments (
    comment_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '评论唯一标识',
    product_id INT NOT NULL COMMENT '关联商品ID',
    user_id INT COMMENT '评论用户ID',
    parent_comment_id INT COMMENT '父评论ID（用于回复）',
    content TEXT NOT NULL COMMENT '评论内容',
    likes INT DEFAULT 0 COMMENT '点赞数',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '评论创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '评论最后更新时间',
    status ENUM('active', 'hidden', 'deleted') DEFAULT 'active' COMMENT '评论状态'
) COMMENT='商品评论表';


CREATE TABLE agt_user_likes (
    like_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '点赞记录唯一标识',
    user_id INT NOT NULL COMMENT '点赞用户ID',
    product_id INT COMMENT '点赞商品ID',
    comment_id INT COMMENT '点赞评论ID',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '点赞时间',
    CONSTRAINT check_product_or_comment CHECK (
        (product_id IS NOT NULL AND comment_id IS NULL) OR
        (product_id IS NULL AND comment_id IS NOT NULL)
    ),
    UNIQUE KEY unique_user_product (user_id, product_id),
    UNIQUE KEY unique_user_comment (user_id, comment_id)
) COMMENT='用户点赞记录表';


CREATE TABLE agt_platforms (
    platform_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '平台唯一标识',
    name VARCHAR(100) NOT NULL COMMENT '平台名称',
    logo VARCHAR(255) COMMENT '平台Logo链接',
    description TEXT COMMENT '平台描述',
    url VARCHAR(255) COMMENT '平台网址',
    default_discount_code VARCHAR(50) COMMENT '平台默认折扣码',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '平台状态',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '平台创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '平台最后更新时间'
) COMMENT='第三方购买平台表';

CREATE TABLE agt_product_platforms (
    product_id INT NOT NULL COMMENT '关联商品ID',
    platform_id INT NOT NULL COMMENT '关联平台ID',
    platform_product_url VARCHAR(255) NOT NULL COMMENT '平台商品链接',
    custom_discount_code VARCHAR(50) COMMENT '自定义折扣码',
    platform_price DECIMAL(10,2) COMMENT '平台商品价格',
    status ENUM('active', 'inactive') DEFAULT 'active' COMMENT '关联状态',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '关联创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '关联最后更新时间',
    PRIMARY KEY (product_id, platform_id)
) COMMENT='商品平台关联表';

#创建商品评价表
CREATE TABLE agt_reviews (
    review_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '评价唯一标识',
    product_id INT NOT NULL COMMENT '关联商品ID',
    user_id INT NOT NULL COMMENT '评价用户ID',
    order_item_id INT COMMENT '关联订单商品ID（用于验证购买）',
    rating INT NOT NULL COMMENT '评分（1-5）',
    content TEXT COMMENT '评价内容',
    pros TEXT COMMENT '优点',
    cons TEXT COMMENT '缺点',
    helpful INT DEFAULT 0 COMMENT '有用次数',
    verified_purchase BOOLEAN DEFAULT FALSE COMMENT '是否为已验证购买',
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' COMMENT '评价状态',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '评价创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '评价最后更新时间'
) COMMENT='商品评价表';

#创建评价图片表
CREATE TABLE agt_review_images (
    image_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '评价图片唯一标识',
    review_id INT NOT NULL COMMENT '关联评价ID',
    image_url VARCHAR(255) NOT NULL COMMENT '图片链接',
    display_order INT DEFAULT 0 COMMENT '显示顺序',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '图片创建时间'
) COMMENT='评价图片表';

#创建用户设置表
CREATE TABLE agt_user_settings (
    setting_id INT PRIMARY KEY AUTO_INCREMENT COMMENT '设置唯一标识',
    user_id INT NOT NULL UNIQUE COMMENT '关联用户ID',
    default_currency VARCHAR(3) DEFAULT 'USD' COMMENT '默认货币',
    default_platform_id INT COMMENT '默认平台ID',
    notifications_enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用通知',
    language_preference VARCHAR(10) DEFAULT 'en' COMMENT '语言偏好',
    theme_preference VARCHAR(20) DEFAULT 'light' COMMENT '主题偏好',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '设置创建时间',
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '设置最后更新时间'
) COMMENT='用户设置表';
