package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.AgtTags;
import com.ylkj.system.model.vo.agtTags.AgtTagsVo;
import com.ylkj.system.model.dto.agtTags.AgtTagsQuery;
import com.ylkj.system.model.dto.agtTags.AgtTagsInsert;
import com.ylkj.system.model.dto.agtTags.AgtTagsEdit;
import com.ylkj.system.service.IAgtTagsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 标签Controller
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@RestController
@RequestMapping("/system/AgtTags")
public class AgtTagsController extends BaseController
{
    @Resource
    private IAgtTagsService agtTagsService;

    /**
     * 查询标签列表
     */
    @PreAuthorize("@ss.hasPermi('system:AgtTags:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgtTagsQuery agtTagsQuery)
    {
        AgtTags agtTags = AgtTagsQuery.queryToObj(agtTagsQuery);
        startPage();
        List<AgtTags> list = agtTagsService.selectAgtTagsList(agtTags);
        List<AgtTagsVo> listVo= list.stream().map(AgtTagsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出标签列表
     */
    @PreAuthorize("@ss.hasPermi('system:AgtTags:export')")
    @Log(title = "标签", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtTagsQuery agtTagsQuery)
    {
        AgtTags agtTags = AgtTagsQuery.queryToObj(agtTagsQuery);
        List<AgtTags> list = agtTagsService.selectAgtTagsList(agtTags);
        ExcelUtil<AgtTags> util = new ExcelUtil<AgtTags>(AgtTags.class);
        util.exportExcel(response, list, "标签数据");
    }

    /**
     * 获取标签详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:AgtTags:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        AgtTags agtTags = agtTagsService.selectAgtTagsById(id);
        return success(AgtTagsVo.objToVo(agtTags));
    }

    /**
     * 新增标签
     */
    @PreAuthorize("@ss.hasPermi('system:AgtTags:add')")
    @Log(title = "标签", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgtTagsInsert agtTagsInsert)
    {
        AgtTags agtTags = AgtTagsInsert.insertToObj(agtTagsInsert);
        return toAjax(agtTagsService.insertAgtTags(agtTags));
    }

    /**
     * 修改标签
     */
    @PreAuthorize("@ss.hasPermi('system:AgtTags:edit')")
    @Log(title = "标签", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgtTagsEdit agtTagsEdit)
    {
        AgtTags agtTags = AgtTagsEdit.editToObj(agtTagsEdit);
        return toAjax(agtTagsService.updateAgtTags(agtTags));
    }

    /**
     * 删除标签
     */
    @PreAuthorize("@ss.hasPermi('system:AgtTags:remove')")
    @Log(title = "标签", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(agtTagsService.deleteAgtTagsByIds(ids));
    }
}
