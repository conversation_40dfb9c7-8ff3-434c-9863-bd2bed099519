package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgQcItems;
import com.ylkj.system.model.vo.omgQcItems.OmgQcItemsVo;
import com.ylkj.system.model.dto.omgQcItems.OmgQcItemsQuery;
import com.ylkj.system.model.dto.omgQcItems.OmgQcItemsInsert;
import com.ylkj.system.model.dto.omgQcItems.OmgQcItemsEdit;
import com.ylkj.system.service.IOmgQcItemsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * omg 首页qc图Controller
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@RestController
@RequestMapping("/system/OmgItems")
public class OmgQcItemsController extends BaseController
{
    @Resource
    private IOmgQcItemsService omgQcItemsService;

    /**
     * 查询omg 首页qc图列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgItems:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgQcItemsQuery omgQcItemsQuery)
    {
        OmgQcItems omgQcItems = OmgQcItemsQuery.queryToObj(omgQcItemsQuery);
        startPage();
        List<OmgQcItems> list = omgQcItemsService.selectOmgQcItemsList(omgQcItems);
        List<OmgQcItemsVo> listVo= list.stream().map(OmgQcItemsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出omg 首页qc图列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgItems:export')")
    @Log(title = "omg 首页qc图", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgQcItemsQuery omgQcItemsQuery)
    {
        OmgQcItems omgQcItems = OmgQcItemsQuery.queryToObj(omgQcItemsQuery);
        List<OmgQcItems> list = omgQcItemsService.selectOmgQcItemsList(omgQcItems);
        ExcelUtil<OmgQcItems> util = new ExcelUtil<OmgQcItems>(OmgQcItems.class);
        util.exportExcel(response, list, "omg 首页qc图数据");
    }

    /**
     * 获取omg 首页qc图详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:OmgItems:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgQcItems omgQcItems = omgQcItemsService.selectOmgQcItemsById(id);
        return success(OmgQcItemsVo.objToVo(omgQcItems));
    }

    /**
     * 新增omg 首页qc图
     */
    @PreAuthorize("@ss.hasPermi('system:OmgItems:add')")
    @Log(title = "omg 首页qc图", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgQcItemsInsert omgQcItemsInsert)
    {
        OmgQcItems omgQcItems = OmgQcItemsInsert.insertToObj(omgQcItemsInsert);
        return toAjax(omgQcItemsService.insertOmgQcItems(omgQcItems));
    }

    /**
     * 修改omg 首页qc图
     */
    @PreAuthorize("@ss.hasPermi('system:OmgItems:edit')")
    @Log(title = "omg 首页qc图", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgQcItemsEdit omgQcItemsEdit)
    {
        OmgQcItems omgQcItems = OmgQcItemsEdit.editToObj(omgQcItemsEdit);
        return toAjax(omgQcItemsService.updateOmgQcItems(omgQcItems));
    }

    /**
     * 删除omg 首页qc图
     */
    @PreAuthorize("@ss.hasPermi('system:OmgItems:remove')")
    @Log(title = "omg 首页qc图", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgQcItemsService.deleteOmgQcItemsByIds(ids));
    }
}
