package com.ylkj.common.config;

import com.aliyun.oss.OSS;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.annotation.PreDestroy;

/**
 * 阿里云OSS配置类
 *
 * <AUTHOR>
 */
@Configuration
@ConfigurationProperties(prefix = "oss")
public class OssConfig {
    /**
     * OSS访问端点
     */
    private String endpoint;

    /**
     * 访问身份验证ID
     */
    private String accessKeyId;

    /**
     * 访问身份验证Secret
     */
    private String accessKeySecret;

    /**
     * OSS的Bucket名称
     */
    private String bucketName;

    /**
     * 访问文件的URL前缀
     */
    private String urlPrefix;

    /**
     * 上传文件最大大小，单位MB
     */
    private Long maxSize;

    /**
     * 链接有效期，单位秒
     */
    private Long expireTime;

    /**
     * 文件存放目录
     */
    private String dir;

    /**
     * 访问网站前缀（用于替换OSS URL前缀）
     */
    private String visitWebsite;

    /**
     * OSS客户端
     */
    private OSS ossClient;

    /**
     * 创建OSS客户端实例
     */
    @Bean
    public com.aliyun.oss.OSS ossClient() {
        ossClient = new com.aliyun.oss.OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
        return ossClient;
    }
    
    /**
     * 销毁OSS客户端
     */
    @PreDestroy
    public void destroy() {
        if (ossClient != null) {
            ossClient.shutdown();
        }
    }
    
    // Getters and Setters
    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public void setAccessKeyId(String accessKeyId) {
        this.accessKeyId = accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public void setAccessKeySecret(String accessKeySecret) {
        this.accessKeySecret = accessKeySecret;
    }

    public String getBucketName() {
        return bucketName;
    }

    public void setBucketName(String bucketName) {
        this.bucketName = bucketName;
    }

    public String getUrlPrefix() {
        return urlPrefix;
    }

    public void setUrlPrefix(String urlPrefix) {
        this.urlPrefix = urlPrefix;
    }

    public Long getMaxSize() {
        return maxSize;
    }

    public void setMaxSize(Long maxSize) {
        this.maxSize = maxSize * 1024 * 1024; // 转换为字节
    }

    public Long getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Long expireTime) {
        this.expireTime = expireTime;
    }

    public String getDir() {
        return dir;
    }

    public void setDir(String dir) {
        this.dir = dir;
    }

    public String getVisitWebsite() {
        return visitWebsite;
    }

    public void setVisitWebsite(String visitWebsite) {
        this.visitWebsite = visitWebsite;
    }
}