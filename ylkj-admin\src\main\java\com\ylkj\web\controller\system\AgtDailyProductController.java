package com.ylkj.web.controller.system;

import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.core.page.TableDataInfo;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.system.model.domain.AgtDailyProduct;
import com.ylkj.system.model.dto.agtDailyProduct.AgtDailyProductEdit;
import com.ylkj.system.model.dto.agtDailyProduct.AgtDailyProductInsert;
import com.ylkj.system.model.dto.agtDailyProduct.AgtDailyProductQuery;
import com.ylkj.system.model.vo.agtDailyProduct.AgtDailyProductVo;
import com.ylkj.system.service.IAgtDailyProductService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import java.util.List;
import java.util.stream.Collectors;

import static com.ylkj.common.utils.PageUtils.startPage;

/**
 * @BelongsProject: AGTFIND-backbend
 * @BelongsPackage: com.ylkj.web.controller.system
 * @Author: 小林
 * @CreateTime: 2025-04-30 11:00
 * @Description: 每日一品控制层
 * @Version: 1.0
 */
@RestController
@RequestMapping("/system/dailyProduct")
public class AgtDailyProductController extends BaseController {

    @Resource
    private IAgtDailyProductService agtDailyProductService;

    /**
     * 添加每日一品
     * @param agtDailyProduct
     * @return
     */
    @PostMapping("/addOrUpdate")
    public AjaxResult addOrUpdate(@RequestBody AgtDailyProduct agtDailyProduct) {
        int result = agtDailyProductService.addOrUpdateDailyProduct(agtDailyProduct);
        if (result > 0) {
            return AjaxResult.success("操作成功");
        } else {
            return AjaxResult.error("操作失败");
        }
    }

    // 判断今天是否已经存在新增记录
    @GetMapping("/checkExist")
    public AjaxResult checkExistTodayDailyProduct() {
        boolean exist = agtDailyProductService.checkExistTodayDailyProduct();
        if (exist) {
            return AjaxResult.success("今天已存在记录");
        } else {
            return AjaxResult.success("今天没有记录");
        }
    }

    // 更新每日一品
    @PostMapping("/update")
    public AjaxResult updateDailyProduct(@RequestBody AgtDailyProduct agtDailyProduct) {
        int result = agtDailyProductService.updateDailyProduct(agtDailyProduct);
        if (result > 0) {
            return AjaxResult.success("更新成功");
        } else {
            return AjaxResult.error("更新失败");
        }
    }

    /**
     * 查询每日一品列表
     */
    @PreAuthorize("@ss.hasPermi('system:dailyProduct:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgtDailyProductQuery agtDailyProductQuery)
    {
        System.err.println(agtDailyProductQuery.getCreatedAt());
        AgtDailyProduct agtDailyProduct = AgtDailyProductQuery.queryToObj(agtDailyProductQuery);
        System.err.println(agtDailyProduct.getCreatedAt());
        startPage();
        List<AgtDailyProduct> list = agtDailyProductService.selectAgtDailyProductList(agtDailyProduct);
        List<AgtDailyProductVo> listVo= list.stream().map(AgtDailyProductVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出每日一品列表
     */
    @PreAuthorize("@ss.hasPermi('system:dailyProduct:export')")
    @Log(title = "每日一品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtDailyProductQuery agtDailyProductQuery)
    {
        AgtDailyProduct agtDailyProduct = AgtDailyProductQuery.queryToObj(agtDailyProductQuery);
        List<AgtDailyProduct> list = agtDailyProductService.selectAgtDailyProductList(agtDailyProduct);
        ExcelUtil<AgtDailyProduct> util = new ExcelUtil<AgtDailyProduct>(AgtDailyProduct.class);
        util.exportExcel(response, list, "每日一品数据");
    }

    /**
     * 获取每日一品详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dailyProduct:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        AgtDailyProduct agtDailyProduct = agtDailyProductService.selectAgtDailyProductById(id);
        return success(AgtDailyProductVo.objToVo(agtDailyProduct));
    }

    /**
     * 新增每日一品
     */
    @PreAuthorize("@ss.hasPermi('system:dailyProduct:add')")
    @Log(title = "每日一品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgtDailyProductInsert agtDailyProductInsert)
    {
        AgtDailyProduct agtDailyProduct = AgtDailyProductInsert.insertToObj(agtDailyProductInsert);
        return toAjax(agtDailyProductService.insertAgtDailyProduct(agtDailyProduct));
    }

    /**
     * 修改每日一品
     */
    @PreAuthorize("@ss.hasPermi('system:dailyProduct:edit')")
    @Log(title = "每日一品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgtDailyProductEdit agtDailyProductEdit)
    {
        AgtDailyProduct agtDailyProduct = AgtDailyProductEdit.editToObj(agtDailyProductEdit);
        return toAjax(agtDailyProductService.updateAgtDailyProduct(agtDailyProduct));
    }

    /**
     * 删除每日一品
     */
    @PreAuthorize("@ss.hasPermi('system:dailyProduct:remove')")
    @Log(title = "每日一品", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(agtDailyProductService.deleteAgtDailyProductByIds(ids));
    }
}