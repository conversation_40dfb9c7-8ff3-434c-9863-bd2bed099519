package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.SOmgCategories;
import com.ylkj.system.model.vo.omgCategories.SOmgCategoriesVo;
import com.ylkj.system.model.dto.omgCategories.SOmgCategoriesQuery;
import com.ylkj.system.model.dto.omgCategories.SOmgCategoriesInsert;
import com.ylkj.system.model.dto.omgCategories.SOmgCategoriesEdit;
import com.ylkj.system.service.SIOmgCategoriesService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * omg_分类Controller
 *
 * <AUTHOR>
 * @date 2025-05-14
 */
@RestController
@RequestMapping("/system/OmgCategories")
public class SOmgCategoriesController extends BaseController
{
    @Resource
    private SIOmgCategoriesService sOmgCategoriesService;

    /**
     * 查询omg_分类列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgCategories:list')")
    @GetMapping("/list")
    public TableDataInfo list(SOmgCategoriesQuery omgCategoriesQuery)
    {
        SOmgCategories omgCategories = SOmgCategoriesQuery.queryToObj(omgCategoriesQuery);
        startPage();
        List<SOmgCategories> list = sOmgCategoriesService.selectOmgCategoriesList(omgCategories);
        List<SOmgCategoriesVo> listVo= list.stream().map(SOmgCategoriesVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出omg_分类列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgCategories:export')")
    @Log(title = "omg_分类", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SOmgCategoriesQuery omgCategoriesQuery)
    {
        SOmgCategories omgCategories = SOmgCategoriesQuery.queryToObj(omgCategoriesQuery);
        List<SOmgCategories> list = sOmgCategoriesService.selectOmgCategoriesList(omgCategories);
        ExcelUtil<SOmgCategories> util = new ExcelUtil<SOmgCategories>(SOmgCategories.class);
        util.exportExcel(response, list, "omg_分类数据");
    }

    /**
     * 获取omg_分类详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:OmgCategories:query')")
    @GetMapping(value = "/{categoryId}")
    public AjaxResult getInfo(@PathVariable("categoryId") Long categoryId)
    {
        SOmgCategories omgCategories = sOmgCategoriesService.selectOmgCategoriesByCategoryId(categoryId);
        return success(SOmgCategoriesVo.objToVo(omgCategories));
    }

    /**
     * 新增omg_分类
     */
    @PreAuthorize("@ss.hasPermi('system:OmgCategories:add')")
    @Log(title = "omg_分类", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SOmgCategoriesInsert omgCategoriesInsert)
    {
        SOmgCategories omgCategories = SOmgCategoriesInsert.insertToObj(omgCategoriesInsert);
        return toAjax(sOmgCategoriesService.insertOmgCategories(omgCategories));
    }

    /**
     * 修改omg_分类
     */
    @PreAuthorize("@ss.hasPermi('system:OmgCategories:edit')")
    @Log(title = "omg_分类", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SOmgCategoriesEdit omgCategoriesEdit)
    {
        SOmgCategories omgCategories = SOmgCategoriesEdit.editToObj(omgCategoriesEdit);
        return toAjax(sOmgCategoriesService.updateOmgCategories(omgCategories));
    }

    /**
     * 删除omg_分类
     */
    @PreAuthorize("@ss.hasPermi('system:OmgCategories:remove')")
    @Log(title = "omg_分类", businessType = BusinessType.DELETE)
	@DeleteMapping("/{categoryIds}")
    public AjaxResult remove(@PathVariable Long[] categoryIds)
    {
        return toAjax(sOmgCategoriesService.deleteOmgCategoriesByCategoryIds(categoryIds));
    }
}
