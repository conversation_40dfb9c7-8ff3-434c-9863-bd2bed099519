package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.AgtCategories;
import com.ylkj.system.model.vo.agtCategories.AgtCategoriesVo;
import com.ylkj.system.model.dto.agtCategories.AgtCategoriesQuery;
import com.ylkj.system.model.dto.agtCategories.AgtCategoriesInsert;
import com.ylkj.system.model.dto.agtCategories.AgtCategoriesEdit;
import com.ylkj.system.service.IAgtCategoriesService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 分类管理Controller
 *
 * <AUTHOR>
 * @date 2025-04-29
 */
@RestController
@RequestMapping("/system/categories")
public class AgtCategoriesController extends BaseController
{
    @Resource
    private IAgtCategoriesService agtCategoriesService;

    /**
     * 查询分类管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:categories:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgtCategoriesQuery agtCategoriesQuery)
    {
        AgtCategories agtCategories = AgtCategoriesQuery.queryToObj(agtCategoriesQuery);
        startPage();
        List<AgtCategories> list = agtCategoriesService.selectAgtCategoriesList(agtCategories);
        List<AgtCategoriesVo> listVo= list.stream().map(AgtCategoriesVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 获取所有分类列表
     */
    @PreAuthorize("@ss.hasPermi('system:categories:list')")
    @GetMapping("/all")
    public AjaxResult getAllCategories()
    {
        List<AgtCategories> list = agtCategoriesService.selectAllAgtCategories();
        List<AgtCategoriesVo> listVo = list.stream().map(AgtCategoriesVo::objToVo).collect(Collectors.toList());
        return success(listVo);
    }

    /**
     * 导出分类管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:categories:export')")
    @Log(title = "分类管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtCategoriesQuery agtCategoriesQuery)
    {
        AgtCategories agtCategories = AgtCategoriesQuery.queryToObj(agtCategoriesQuery);
        List<AgtCategories> list = agtCategoriesService.selectAgtCategoriesList(agtCategories);
        ExcelUtil<AgtCategories> util = new ExcelUtil<AgtCategories>(AgtCategories.class);
        util.exportExcel(response, list, "分类管理数据");
    }

    /**
     * 获取分类管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:categories:query')")
    @GetMapping(value = "/{categoryId}")
    public AjaxResult getInfo(@PathVariable("categoryId") Long categoryId)
    {
        AgtCategories agtCategories = agtCategoriesService.selectAgtCategoriesByCategoryId(categoryId);
        return success(AgtCategoriesVo.objToVo(agtCategories));
    }

    /**
     * 新增分类管理
     */
    @PreAuthorize("@ss.hasPermi('system:categories:add')")
    @Log(title = "分类管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgtCategoriesInsert agtCategoriesInsert)
    {
        AgtCategories agtCategories = AgtCategoriesInsert.insertToObj(agtCategoriesInsert);
        return toAjax(agtCategoriesService.insertAgtCategories(agtCategories));
    }

    /**
     * 修改分类管理
     */
    @PreAuthorize("@ss.hasPermi('system:categories:edit')")
    @Log(title = "分类管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgtCategoriesEdit agtCategoriesEdit)
    {
        AgtCategories agtCategories = AgtCategoriesEdit.editToObj(agtCategoriesEdit);
        return toAjax(agtCategoriesService.updateAgtCategories(agtCategories));
    }

    /**
     * 删除分类管理
     */
    @PreAuthorize("@ss.hasPermi('system:categories:remove')")
    @Log(title = "分类管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{categoryIds}")
    public AjaxResult remove(@PathVariable Long[] categoryIds)
    {
        return toAjax(agtCategoriesService.deleteAgtCategoriesByCategoryIds(categoryIds));
    }
}
