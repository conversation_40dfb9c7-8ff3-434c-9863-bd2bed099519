package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgPointMallGoods;
import com.ylkj.system.model.vo.omgPointMallGoods.OmgPointMallGoodsVo;
import com.ylkj.system.model.dto.omgPointMallGoods.OmgPointMallGoodsQuery;
import com.ylkj.system.model.dto.omgPointMallGoods.OmgPointMallGoodsInsert;
import com.ylkj.system.model.dto.omgPointMallGoods.OmgPointMallGoodsEdit;
import com.ylkj.system.service.IOmgPointMallGoodsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 积分商城Controller
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@RestController
@RequestMapping("/system/OmgGoods")
public class OmgPointMallGoodsController extends BaseController
{
    @Resource
    private IOmgPointMallGoodsService omgPointMallGoodsService;

    /**
     * 查询积分商城列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgGoods:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgPointMallGoodsQuery omgPointMallGoodsQuery)
    {
        OmgPointMallGoods omgPointMallGoods = OmgPointMallGoodsQuery.queryToObj(omgPointMallGoodsQuery);
        startPage();
        List<OmgPointMallGoods> list = omgPointMallGoodsService.selectOmgPointMallGoodsList(omgPointMallGoods);
        List<OmgPointMallGoodsVo> listVo= list.stream().map(OmgPointMallGoodsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出积分商城列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgGoods:export')")
    @Log(title = "积分商城", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgPointMallGoodsQuery omgPointMallGoodsQuery)
    {
        OmgPointMallGoods omgPointMallGoods = OmgPointMallGoodsQuery.queryToObj(omgPointMallGoodsQuery);
        List<OmgPointMallGoods> list = omgPointMallGoodsService.selectOmgPointMallGoodsList(omgPointMallGoods);
        ExcelUtil<OmgPointMallGoods> util = new ExcelUtil<OmgPointMallGoods>(OmgPointMallGoods.class);
        util.exportExcel(response, list, "积分商城数据");
    }

    /**
     * 获取积分商城详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:OmgGoods:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgPointMallGoods omgPointMallGoods = omgPointMallGoodsService.selectOmgPointMallGoodsById(id);
        return success(OmgPointMallGoodsVo.objToVo(omgPointMallGoods));
    }

    /**
     * 新增积分商城
     */
    @PreAuthorize("@ss.hasPermi('system:OmgGoods:add')")
    @Log(title = "积分商城", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgPointMallGoodsInsert omgPointMallGoodsInsert)
    {
        OmgPointMallGoods omgPointMallGoods = OmgPointMallGoodsInsert.insertToObj(omgPointMallGoodsInsert);
        return toAjax(omgPointMallGoodsService.insertOmgPointMallGoods(omgPointMallGoods));
    }

    /**
     * 修改积分商城
     */
    @PreAuthorize("@ss.hasPermi('system:OmgGoods:edit')")
    @Log(title = "积分商城", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgPointMallGoodsEdit omgPointMallGoodsEdit)
    {
        OmgPointMallGoods omgPointMallGoods = OmgPointMallGoodsEdit.editToObj(omgPointMallGoodsEdit);
        return toAjax(omgPointMallGoodsService.updateOmgPointMallGoods(omgPointMallGoods));
    }

    /**
     * 删除积分商城
     */
    @PreAuthorize("@ss.hasPermi('system:OmgGoods:remove')")
    @Log(title = "积分商城", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgPointMallGoodsService.deleteOmgPointMallGoodsByIds(ids));
    }
}
