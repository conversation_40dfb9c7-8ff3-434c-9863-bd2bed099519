package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.UserPoints;
import com.ylkj.system.model.vo.userPoints.UserPointsVo;
import com.ylkj.system.model.dto.userPoints.UserPointsQuery;
import com.ylkj.system.model.dto.userPoints.UserPointsInsert;
import com.ylkj.system.model.dto.userPoints.UserPointsEdit;
import com.ylkj.system.service.IUserPointsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 用户积分Controller
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RestController
@RequestMapping("/system/points")
public class UserPointsController extends BaseController
{
    @Resource
    private IUserPointsService userPointsService;

    /**
     * 查询用户积分列表
     */
    @PreAuthorize("@ss.hasPermi('system:points:list')")
    @GetMapping("/list")
    public TableDataInfo list(UserPointsQuery userPointsQuery)
    {
        UserPoints userPoints = UserPointsQuery.queryToObj(userPointsQuery);
        startPage();
        List<UserPoints> list = userPointsService.selectUserPointsList(userPoints);
        List<UserPointsVo> listVo= list.stream().map(UserPointsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出用户积分列表
     */
    @PreAuthorize("@ss.hasPermi('system:points:export')")
    @Log(title = "用户积分", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, UserPointsQuery userPointsQuery)
    {
        UserPoints userPoints = UserPointsQuery.queryToObj(userPointsQuery);
        List<UserPoints> list = userPointsService.selectUserPointsList(userPoints);
        ExcelUtil<UserPoints> util = new ExcelUtil<UserPoints>(UserPoints.class);
        util.exportExcel(response, list, "用户积分数据");
    }

    /**
     * 获取用户积分详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:points:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        UserPoints userPoints = userPointsService.selectUserPointsById(id);
        return success(UserPointsVo.objToVo(userPoints));
    }

    /**
     * 新增用户积分
     */
    @PreAuthorize("@ss.hasPermi('system:points:add')")
    @Log(title = "用户积分", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody UserPointsInsert userPointsInsert)
    {
        UserPoints userPoints = UserPointsInsert.insertToObj(userPointsInsert);
        return toAjax(userPointsService.insertUserPoints(userPoints));
    }

    /**
     * 修改用户积分
     */
    @PreAuthorize("@ss.hasPermi('system:points:edit')")
    @Log(title = "用户积分", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody UserPointsEdit userPointsEdit)
    {
        UserPoints userPoints = UserPointsEdit.editToObj(userPointsEdit);
        return toAjax(userPointsService.updateUserPoints(userPoints));
    }

    /**
     * 删除用户积分
     */
    @PreAuthorize("@ss.hasPermi('system:points:remove')")
    @Log(title = "用户积分", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(userPointsService.deleteUserPointsByIds(ids));
    }
}
