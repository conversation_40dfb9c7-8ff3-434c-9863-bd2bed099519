package com.ylkj.web.controller.system;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgComments;
import com.ylkj.system.model.vo.omgComments.OmgCommentsVo;
import com.ylkj.system.model.dto.omgComments.OmgCommentsQuery;
import com.ylkj.system.model.dto.omgComments.OmgCommentsInsert;
import com.ylkj.system.model.dto.omgComments.OmgCommentsEdit;
import com.ylkj.system.service.IOmgCommentsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * omg_评论Controller
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@RestController
@RequestMapping("/system/OmgComment")
public class OmgCommentsController extends BaseController
{
    @Resource
    private IOmgCommentsService omgCommentsService;

    /**
     * 查询omg_评论列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgComment:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgCommentsQuery omgCommentsQuery)
    {
        OmgComments omgComments = OmgCommentsQuery.queryToObj(omgCommentsQuery);
        startPage();
        List<OmgComments> list = omgCommentsService.selectOmgCommentsList(omgComments);
        List<OmgCommentsVo> listVo= list.stream().map(OmgCommentsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出omg_评论列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgComment:export')")
    @Log(title = "omg_评论", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgCommentsQuery omgCommentsQuery)
    {
        OmgComments omgComments = OmgCommentsQuery.queryToObj(omgCommentsQuery);
        List<OmgComments> list = omgCommentsService.selectOmgCommentsList(omgComments);
        ExcelUtil<OmgComments> util = new ExcelUtil<OmgComments>(OmgComments.class);
        util.exportExcel(response, list, "omg_评论数据");
    }

    /**
     * 获取omg_评论详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:OmgComment:query')")
    @GetMapping(value = "/{commentId}")
    public AjaxResult getInfo(@PathVariable("commentId") Long commentId)
    {
        OmgComments omgComments = omgCommentsService.selectOmgCommentsByCommentId(commentId);
        return success(OmgCommentsVo.objToVo(omgComments));
    }

    /**
     * 新增omg_评论
     */
    @PreAuthorize("@ss.hasPermi('system:OmgComment:add')")
    @Log(title = "omg_评论", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgCommentsInsert omgCommentsInsert)
    {
        OmgComments omgComments = OmgCommentsInsert.insertToObj(omgCommentsInsert);
        return toAjax(omgCommentsService.insertOmgComments(omgComments));
    }

    /**
     * 修改omg_评论
     */
    @PreAuthorize("@ss.hasPermi('system:OmgComment:edit')")
    @Log(title = "omg_评论", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgCommentsEdit omgCommentsEdit)
    {
        OmgComments omgComments = OmgCommentsEdit.editToObj(omgCommentsEdit);
        return toAjax(omgCommentsService.updateOmgComments(omgComments));
    }

    /**
     * 删除omg_评论
     */
    @PreAuthorize("@ss.hasPermi('system:OmgComment:remove')")
    @Log(title = "omg_评论", businessType = BusinessType.DELETE)
	@DeleteMapping("/{commentIds}")
    public AjaxResult remove(@PathVariable Long[] commentIds)
    {
        return toAjax(omgCommentsService.deleteOmgCommentsByCommentIds(commentIds));
    }

    /**
     * 生成测试用户和评论
     */
    @PostMapping("/testData/generate")
    @PreAuthorize("@ss.hasPermi('system:OmgComment:add')")
    public AjaxResult generateData(@RequestBody Map<String, Object> params) {
        // 从请求体中获取参数
        Integer userCount = params.get("userCount") == null ?
                5 : Integer.parseInt(params.get("userCount").toString());
        Long commentCount = params.get("commentCount") == null ?
                20L : Long.parseLong(params.get("commentCount").toString());
        Long productId = Long.parseLong(params.get("productId").toString());
        List<String> commentContents = (List<String>) params.get("commentContents");

        List<OmgComments> comments = omgCommentsService.generateTestComments(userCount,productId, commentCount,commentContents);

        return success(new HashMap<String, Object>() {{
            put("count", comments.size());
            put("message", "成功生成" + userCount + "个用户和" + comments.size() + "条评论");
        }});
    }
}
