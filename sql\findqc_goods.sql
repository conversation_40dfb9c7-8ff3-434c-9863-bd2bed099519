-- ----------------------------
-- Findqc商品信息表
-- ----------------------------
DROP TABLE IF EXISTS `findqc_goods`;
CREATE TABLE `findqc_goods` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_id` varchar(100) NOT NULL COMMENT '商品ID',
  `goods_name` varchar(500) DEFAULT NULL COMMENT '商品名称',
  `description` text COMMENT '商品描述',
  `price` decimal(10,2) DEFAULT NULL COMMENT '商品价格',
  `currency_type` varchar(10) DEFAULT 'USD' COMMENT '货币类型',
  `image_url` varchar(1000) DEFAULT NULL COMMENT '商品图片URL',
  `category` varchar(200) DEFAULT NULL COMMENT '商品分类',
  `supplier_name` varchar(200) DEFAULT NULL COMMENT '供应商名称',
  `stock_quantity` int(11) DEFAULT NULL COMMENT '库存数量',
  `status` varchar(50) DEFAULT NULL COMMENT '商品状态',
  `lang_type` varchar(10) DEFAULT 'en' COMMENT '语言类型',
  `item_id` varchar(100) DEFAULT NULL COMMENT '商品条目ID',
  `view_count` int(11) DEFAULT NULL COMMENT '浏览次数',
  `qc_detail` varchar(50) DEFAULT NULL COMMENT 'QC详情数量',
  `to_price` varchar(20) DEFAULT NULL COMMENT '转换后价格(USD)',
  `mall_type` varchar(20) DEFAULT NULL COMMENT '商城类型',
  `original_data` longtext COMMENT '原始数据JSON',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_goods_id` (`goods_id`),
  KEY `idx_goods_name` (`goods_name`(100)),
  KEY `idx_category` (`category`),
  KEY `idx_supplier` (`supplier_name`),
  KEY `idx_mall_type` (`mall_type`),
  KEY `idx_item_id` (`item_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='Findqc商品信息表'; 