package com.ylkj.model.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: 吴居乐
 * @Description: TODO
 * @DateTime: 2025/8/2/周六 17:34
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SyncResult {

    private int total = 0;
    private int success = 0;
    private int failed = 0;



    public void incrementSuccess() {
        this.success++;
    }

    public void incrementFailed() {
        this.failed++;
    }

}
