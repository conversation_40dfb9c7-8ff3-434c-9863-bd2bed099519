-- ----------------------------
-- QC SPU信息表
-- ----------------------------
CREATE TABLE `qc_spu_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `spu_id` bigint(20) DEFAULT NULL COMMENT 'SPU ID',
  `name` varchar(255) DEFAULT NULL COMMENT 'QC名称',
  `item_id` varchar(100) NOT NULL COMMENT '商品ID',
  `mall_type` varchar(50) DEFAULT NULL COMMENT '商城类型',
  `en_title` text DEFAULT NULL COMMENT '英文标题',
  `original_title` text DEFAULT NULL COMMENT '原始标题',
  `pic_url` varchar(500) DEFAULT NULL COMMENT '图片URL',
  `qc_photos_count` int(11) DEFAULT 0 COMMENT 'QC图片数量',
  `status` tinyint(4) DEFAULT 1 COMMENT '状态',
  `original_data` longtext DEFAULT NULL COMMENT '原始数据JSON',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_item_id` (`item_id`),
  KEY `idx_spu_id` (`spu_id`),
  KEY `idx_mall_type` (`mall_type`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='QC SPU信息表';

-- ----------------------------
-- QC SPU名称表
-- ----------------------------
CREATE TABLE `qc_spu_name` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `qc_spu_info_id` bigint(20) NOT NULL COMMENT 'QC SPU信息ID',
  `api_id` bigint(20) DEFAULT NULL COMMENT 'API返回的ID',
  `spu_id` bigint(20) DEFAULT NULL COMMENT 'SPU ID',
  `name` varchar(500) DEFAULT NULL COMMENT 'QC名称',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_qc_spu_info_id` (`qc_spu_info_id`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_spu_id` (`spu_id`),
  CONSTRAINT `fk_qc_spu_name_info_id` FOREIGN KEY (`qc_spu_info_id`) REFERENCES `qc_spu_info` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='QC SPU名称表';

-- ----------------------------
-- QC SPU图片表
-- ----------------------------
CREATE TABLE `qc_spu_image` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `qc_spu_id` bigint(20) DEFAULT NULL COMMENT 'QC SPU ID',
  `qc_spu_name_id` bigint(20) NOT NULL COMMENT 'QC SPU名称ID',
  `api_id` bigint(20) DEFAULT NULL COMMENT 'API返回的ID',
  `url` varchar(500) NOT NULL COMMENT '图片URL',
  `attributes` varchar(255) DEFAULT NULL COMMENT '属性',
  `display_order` int(11) DEFAULT 1 COMMENT '显示顺序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_qc_spu_id` (`qc_spu_id`),
  KEY `idx_qc_spu_name_id` (`qc_spu_name_id`),
  KEY `idx_api_id` (`api_id`),
  KEY `idx_display_order` (`display_order`),
  CONSTRAINT `fk_qc_spu_image_name_id` FOREIGN KEY (`qc_spu_name_id`) REFERENCES `qc_spu_name` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='QC SPU图片表'; 