package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgDatamanagement;
import com.ylkj.system.model.vo.omgDatamanagement.OmgDatamanagementVo;
import com.ylkj.system.model.dto.omgDatamanagement.OmgDatamanagementQuery;
import com.ylkj.system.model.dto.omgDatamanagement.OmgDatamanagementInsert;
import com.ylkj.system.model.dto.omgDatamanagement.OmgDatamanagementEdit;
import com.ylkj.system.service.IOmgDatamanagementService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * omg资料Controller
 *
 * <AUTHOR>
 * @date 2025-05-26
 */
@RestController
@RequestMapping("/system/datamanagement")
public class OmgDatamanagementController extends BaseController
{
    @Resource
    private IOmgDatamanagementService omgDatamanagementService;

    /**
     * 查询omg资料列表
     */
    @PreAuthorize("@ss.hasPermi('system:datamanagement:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgDatamanagementQuery omgDatamanagementQuery)
    {
        OmgDatamanagement omgDatamanagement = OmgDatamanagementQuery.queryToObj(omgDatamanagementQuery);
        startPage();
        List<OmgDatamanagement> list = omgDatamanagementService.selectOmgDatamanagementList(omgDatamanagement);
        List<OmgDatamanagementVo> listVo= list.stream().map(OmgDatamanagementVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出omg资料列表
     */
    @PreAuthorize("@ss.hasPermi('system:datamanagement:export')")
    @Log(title = "omg资料", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgDatamanagementQuery omgDatamanagementQuery)
    {
        OmgDatamanagement omgDatamanagement = OmgDatamanagementQuery.queryToObj(omgDatamanagementQuery);
        List<OmgDatamanagement> list = omgDatamanagementService.selectOmgDatamanagementList(omgDatamanagement);
        ExcelUtil<OmgDatamanagement> util = new ExcelUtil<OmgDatamanagement>(OmgDatamanagement.class);
        util.exportExcel(response, list, "omg资料数据");
    }

    /**
     * 获取omg资料详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:datamanagement:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgDatamanagement omgDatamanagement = omgDatamanagementService.selectOmgDatamanagementById(id);
        return success(OmgDatamanagementVo.objToVo(omgDatamanagement));
    }

    /**
     * 新增omg资料
     */
    @PreAuthorize("@ss.hasPermi('system:datamanagement:add')")
    @Log(title = "omg资料", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgDatamanagementInsert omgDatamanagementInsert)
    {
        OmgDatamanagement omgDatamanagement = OmgDatamanagementInsert.insertToObj(omgDatamanagementInsert);
        return toAjax(omgDatamanagementService.insertOmgDatamanagement(omgDatamanagement));
    }

    /**
     * 修改omg资料
     */
    @PreAuthorize("@ss.hasPermi('system:datamanagement:edit')")
    @Log(title = "omg资料", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgDatamanagementEdit omgDatamanagementEdit)
    {
        OmgDatamanagement omgDatamanagement = OmgDatamanagementEdit.editToObj(omgDatamanagementEdit);
        return toAjax(omgDatamanagementService.updateOmgDatamanagement(omgDatamanagement));
    }

    /**
     * 删除omg资料
     */
    @PreAuthorize("@ss.hasPermi('system:datamanagement:remove')")
    @Log(title = "omg资料", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgDatamanagementService.deleteOmgDatamanagementByIds(ids));
    }
}
