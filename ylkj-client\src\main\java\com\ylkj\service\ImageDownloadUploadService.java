package com.ylkj.service;

import com.ylkj.common.utils.file.OssUploadUtils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 图片下载和上传OSS服务
 */
@Slf4j
@Service
public class ImageDownloadUploadService {

    private final OkHttpClient client;
    private final ExecutorService executorService;

    public ImageDownloadUploadService() {
        this.client = new OkHttpClient().newBuilder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .build();
        // 创建线程池用于并发下载
        this.executorService = Executors.newFixedThreadPool(10);
    }

    /**
     * 批量下载图片并上传到OSS
     * 
     * @param imageUrls 图片URL列表
     * @param itemId 商品ID（用于生成文件名前缀）
     * @return 上传到OSS后的URL列表
     */
    public List<String> downloadAndUploadImages(List<String> imageUrls, String itemId) {
        if (imageUrls == null || imageUrls.isEmpty()) {
            log.warn("图片URL列表为空");
            return new ArrayList<>();
        }

        log.info("开始批量下载并上传图片，数量: {}, 商品ID: {}", imageUrls.size(), itemId);
        
        List<CompletableFuture<String>> futures = new ArrayList<>();
        
        // 并发下载和上传图片
        for (int i = 0; i < imageUrls.size(); i++) {
            final String imageUrl = imageUrls.get(i);
            final int index = i;
            
            CompletableFuture<String> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return downloadAndUploadSingleImage(imageUrl, itemId, index);
                } catch (Exception e) {
                    log.error("下载并上传图片失败，URL: {}, error: {}", imageUrl, e.getMessage());
                    return null;
                }
            }, executorService);
            
            futures.add(future);
        }
        
        // 等待所有任务完成
        List<String> ossUrls = new ArrayList<>();
        for (CompletableFuture<String> future : futures) {
            try {
                String ossUrl = future.get(120, TimeUnit.SECONDS); // 2分钟超时
                if (ossUrl != null) {
                    ossUrls.add(ossUrl);
                }
            } catch (Exception e) {
                log.error("获取上传结果异常: {}", e.getMessage());
            }
        }
        
        log.info("批量下载上传完成，成功: {}/{}", ossUrls.size(), imageUrls.size());
        return ossUrls;
    }

    /**
     * 下载单个图片并上传到OSS
     * 
     * @param imageUrl 图片URL
     * @param itemId 商品ID
     * @param index 图片索引
     * @return OSS URL
     */
    private String downloadAndUploadSingleImage(String imageUrl, String itemId, int index) {
        try {
            log.debug("开始下载图片: {}", imageUrl);
            
            // 1. 下载图片
            byte[] imageBytes = downloadImage(imageUrl);
            if (imageBytes == null || imageBytes.length == 0) {
                log.warn("下载图片失败或图片为空: {}", imageUrl);
                return null;
            }
            
            // 2. 生成文件名
            String fileName = generateFileName(imageUrl, itemId, index);
            
            // 3. 创建MultipartFile对象
            MultipartFile multipartFile = createMultipartFile(imageBytes, fileName, imageUrl);
            
            // 4. 上传到OSS
            String ossUrl = OssUploadUtils.upload(multipartFile);
            
            log.debug("图片上传成功: {} -> {}", imageUrl, ossUrl);
            return ossUrl;
            
        } catch (Exception e) {
            log.error("下载并上传图片异常，URL: {}, error: {}", imageUrl, e.getMessage());
            return null;
        }
    }

    /**
     * 下载图片
     * 
     * @param imageUrl 图片URL
     * @return 图片字节数组
     */
    private byte[] downloadImage(String imageUrl) {
        try {
            Request request = new Request.Builder()
                    .url(imageUrl)
                    .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
                    .addHeader("Accept", "image/webp,image/apng,image/*,*/*;q=0.8")
                    .addHeader("Accept-Language", "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7")
                    .build();

            try (Response response = client.newCall(request).execute()) {
                if (response.isSuccessful() && response.body() != null) {
                    return response.body().bytes();
                } else {
                    log.warn("下载图片失败，状态码: {}, URL: {}", response.code(), imageUrl);
                    return null;
                }
            }
        } catch (IOException e) {
            log.error("下载图片异常，URL: {}, error: {}", imageUrl, e.getMessage());
            return null;
        }
    }

    /**
     * 生成文件名
     * 
     * @param imageUrl 原始图片URL
     * @param itemId 商品ID
     * @param index 图片索引
     * @return 生成的文件名
     */
    private String generateFileName(String imageUrl, String itemId, int index) {
        // 从URL中提取文件扩展名
        String extension = getFileExtension(imageUrl);
        if (extension.isEmpty()) {
            extension = "jpg"; // 默认扩展名
        }
        
        // 生成文件名：itemId_index.extension
        return String.format("%s_%03d.%s", itemId, index + 1, extension);
    }

    /**
     * 从URL中提取文件扩展名
     * 
     * @param url 图片URL
     * @return 文件扩展名
     */
    private String getFileExtension(String url) {
        try {
            // 移除查询参数
            String cleanUrl = url.split("\\?")[0];
            
            // 提取扩展名
            int lastDotIndex = cleanUrl.lastIndexOf('.');
            if (lastDotIndex > 0 && lastDotIndex < cleanUrl.length() - 1) {
                String extension = cleanUrl.substring(lastDotIndex + 1).toLowerCase();
                
                // 验证是否为有效的图片扩展名
                if (extension.matches("^(jpg|jpeg|png|gif|webp|bmp)$")) {
                    return extension;
                }
            }
        } catch (Exception e) {
            log.debug("提取文件扩展名失败: {}", url);
        }
        
        return "jpg"; // 默认返回jpg
    }

    /**
     * 创建MultipartFile对象
     *
     * @param imageBytes 图片字节数组
     * @param fileName 文件名
     * @param originalUrl 原始URL
     * @return MultipartFile对象
     */
    private MultipartFile createMultipartFile(byte[] imageBytes, String fileName, String originalUrl) {
        String contentType = getContentType(fileName);

        return new CustomMultipartFile(
                "file",
                fileName,
                contentType,
                imageBytes
        );
    }

    /**
     * 根据文件扩展名获取Content-Type
     * 
     * @param fileName 文件名
     * @return Content-Type
     */
    private String getContentType(String fileName) {
        String extension = getFileExtension(fileName).toLowerCase();
        switch (extension) {
            case "jpg":
            case "jpeg":
                return "image/jpeg";
            case "png":
                return "image/png";
            case "gif":
                return "image/gif";
            case "webp":
                return "image/webp";
            case "bmp":
                return "image/bmp";
            default:
                return "image/jpeg";
        }
    }

    /**
     * 关闭服务时清理资源
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 自定义MultipartFile实现
     */
    private static class CustomMultipartFile implements MultipartFile {
        private final String name;
        private final String originalFilename;
        private final String contentType;
        private final byte[] content;

        public CustomMultipartFile(String name, String originalFilename, String contentType, byte[] content) {
            this.name = name;
            this.originalFilename = originalFilename;
            this.contentType = contentType;
            this.content = content;
        }

        @Override
        public String getName() {
            return name;
        }

        @Override
        public String getOriginalFilename() {
            return originalFilename;
        }

        @Override
        public String getContentType() {
            return contentType;
        }

        @Override
        public boolean isEmpty() {
            return content == null || content.length == 0;
        }

        @Override
        public long getSize() {
            return content != null ? content.length : 0;
        }

        @Override
        public byte[] getBytes() throws IOException {
            return content;
        }

        @Override
        public InputStream getInputStream() throws IOException {
            return new ByteArrayInputStream(content);
        }

        @Override
        public void transferTo(java.io.File dest) throws IOException, IllegalStateException {
            try (java.io.FileOutputStream fos = new java.io.FileOutputStream(dest)) {
                fos.write(content);
            }
        }
    }
}
