package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgPostComments;
import com.ylkj.system.model.vo.omgPostComments.OmgPostCommentsVo;
import com.ylkj.system.model.dto.omgPostComments.OmgPostCommentsQuery;
import com.ylkj.system.model.dto.omgPostComments.OmgPostCommentsInsert;
import com.ylkj.system.model.dto.omgPostComments.OmgPostCommentsEdit;
import com.ylkj.system.service.IOmgPostCommentsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 帖子评论Controller
 *
 * <AUTHOR>
 * @date 2025-05-20
 */
@RestController
@RequestMapping("/system/OmgComments")
public class OmgPostCommentsController extends BaseController
{
    @Resource
    private IOmgPostCommentsService omgPostCommentsService;

    /**
     * 查询帖子评论列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgComments:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgPostCommentsQuery omgPostCommentsQuery)
    {
        OmgPostComments omgPostComments = OmgPostCommentsQuery.queryToObj(omgPostCommentsQuery);
        System.err.println(omgPostComments.getUsername());
        startPage();
        List<OmgPostComments> list = omgPostCommentsService.selectOmgPostCommentsList(omgPostComments);
        List<OmgPostCommentsVo> listVo= list.stream().map(OmgPostCommentsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出帖子评论列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgComments:export')")
    @Log(title = "帖子评论", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgPostCommentsQuery omgPostCommentsQuery)
    {
        OmgPostComments omgPostComments = OmgPostCommentsQuery.queryToObj(omgPostCommentsQuery);
        List<OmgPostComments> list = omgPostCommentsService.selectOmgPostCommentsList(omgPostComments);
        ExcelUtil<OmgPostComments> util = new ExcelUtil<OmgPostComments>(OmgPostComments.class);
        util.exportExcel(response, list, "帖子评论数据");
    }

    /**
     * 获取帖子评论详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:OmgComments:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgPostComments omgPostComments = omgPostCommentsService.selectOmgPostCommentsById(id);
        return success(OmgPostCommentsVo.objToVo(omgPostComments));
    }

    /**
     * 新增帖子评论
     */
    @PreAuthorize("@ss.hasPermi('system:OmgComments:add')")
    @Log(title = "帖子评论", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgPostCommentsInsert omgPostCommentsInsert)
    {
        OmgPostComments omgPostComments = OmgPostCommentsInsert.insertToObj(omgPostCommentsInsert);
        return toAjax(omgPostCommentsService.insertOmgPostComments(omgPostComments));
    }

    /**
     * 修改帖子评论
     */
    @PreAuthorize("@ss.hasPermi('system:OmgComments:edit')")
    @Log(title = "帖子评论", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgPostCommentsEdit omgPostCommentsEdit)
    {
        OmgPostComments omgPostComments = OmgPostCommentsEdit.editToObj(omgPostCommentsEdit);
        return toAjax(omgPostCommentsService.updateOmgPostComments(omgPostComments));
    }

    /**
     * 删除帖子评论
     */
    @PreAuthorize("@ss.hasPermi('system:OmgComments:remove')")
    @Log(title = "帖子评论", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgPostCommentsService.deleteOmgPostCommentsByIds(ids));
    }
}
