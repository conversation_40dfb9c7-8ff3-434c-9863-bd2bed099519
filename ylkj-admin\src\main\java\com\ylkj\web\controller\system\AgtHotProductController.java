package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.AgtHotProduct;
import com.ylkj.system.model.vo.agtHotProduct.AgtHotProductVo;
import com.ylkj.system.model.dto.agtHotProduct.AgtHotProductQuery;
import com.ylkj.system.model.dto.agtHotProduct.AgtHotProductInsert;
import com.ylkj.system.model.dto.agtHotProduct.AgtHotProductEdit;
import com.ylkj.system.service.IAgtHotProductService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 最热商品Controller
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RestController
@RequestMapping("/system/Hotproduct")
public class AgtHotProductController extends BaseController
{
    @Resource
    private IAgtHotProductService agtHotProductService;

    /**
     * 查询最热商品列表
     */
    @PreAuthorize("@ss.hasPermi('system:Hotproduct:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgtHotProductQuery agtHotProductQuery)
    {
        AgtHotProduct agtHotProduct = AgtHotProductQuery.queryToObj(agtHotProductQuery);
        startPage();
        List<AgtHotProduct> list = agtHotProductService.selectAgtHotProductList(agtHotProduct);
        List<AgtHotProductVo> listVo= list.stream().map(AgtHotProductVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出最热商品列表
     */
    @PreAuthorize("@ss.hasPermi('system:Hotproduct:export')")
    @Log(title = "最热商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtHotProductQuery agtHotProductQuery)
    {
        AgtHotProduct agtHotProduct = AgtHotProductQuery.queryToObj(agtHotProductQuery);
        List<AgtHotProduct> list = agtHotProductService.selectAgtHotProductList(agtHotProduct);
        ExcelUtil<AgtHotProduct> util = new ExcelUtil<AgtHotProduct>(AgtHotProduct.class);
        util.exportExcel(response, list, "最热商品数据");
    }

    /**
     * 获取最热商品详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:Hotproduct:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        AgtHotProduct agtHotProduct = agtHotProductService.selectAgtHotProductById(id);
        return success(AgtHotProductVo.objToVo(agtHotProduct));
    }

    /**
     * 新增最热商品
     */
    @PreAuthorize("@ss.hasPermi('system:Hotproduct:add')")
    @Log(title = "最热商品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgtHotProductInsert agtHotProductInsert)
    {
        AgtHotProduct agtHotProduct = AgtHotProductInsert.insertToObj(agtHotProductInsert);
        return toAjax(agtHotProductService.insertAgtHotProduct(agtHotProduct));
    }

    /**
     * 修改最热商品
     */
    @PreAuthorize("@ss.hasPermi('system:Hotproduct:edit')")
    @Log(title = "最热商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgtHotProductEdit agtHotProductEdit)
    {
        AgtHotProduct agtHotProduct = AgtHotProductEdit.editToObj(agtHotProductEdit);
        return toAjax(agtHotProductService.updateAgtHotProduct(agtHotProduct));
    }

    /**
     * 删除最热商品
     */
    @PreAuthorize("@ss.hasPermi('system:Hotproduct:remove')")
    @Log(title = "最热商品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(agtHotProductService.deleteAgtHotProductByIds(ids));
    }


    @PostMapping("/batchInsert")
    public AjaxResult batchInsertHotProducts(@RequestBody List<AgtHotProduct> hotProductList) {
        int result = agtHotProductService.batchInsertHotProducts(hotProductList);
        if (result > 0) {
            return AjaxResult.success("批量插入成功");
        } else {
            return AjaxResult.error("批量插入失败");
        }
    }


    // 检查今天是否已经存在记录
    @GetMapping("/checkExist")
    public AjaxResult checkExistTodayHotProducts() {
        boolean exist = agtHotProductService.checkExistTodayHotProducts();
        if (exist) {
            return AjaxResult.success("今天已存在记录");
        } else {
            return AjaxResult.success("今天没有记录");
        }
    }

    // 批量更新最热商品
    @PostMapping("/batchUpdate")
    public AjaxResult batchUpdateHotProducts(@RequestBody List<AgtHotProduct> hotProductList) {
        int result = agtHotProductService.batchUpdateHotProducts(hotProductList);
        if (result > 0) {
            return AjaxResult.success("批量更新成功");
        } else {
            return AjaxResult.error("批量更新失败");
        }
    }
}
