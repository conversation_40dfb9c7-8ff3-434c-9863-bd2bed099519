package com.ylkj.web.controller.system;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.ylkj.system.model.vo.omgTrackingLinks.BatchGenerateRequest;
import com.ylkj.system.model.vo.omgTrackingLinks.OmgTrackingLinksImportVo;
import org.mybatis.logging.Logger;
import org.mybatis.logging.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgTrackingLinks;
import com.ylkj.system.model.vo.omgTrackingLinks.OmgTrackingLinksVo;
import com.ylkj.system.model.dto.omgTrackingLinks.OmgTrackingLinksQuery;
import com.ylkj.system.model.dto.omgTrackingLinks.OmgTrackingLinksInsert;
import com.ylkj.system.model.dto.omgTrackingLinks.OmgTrackingLinksEdit;
import com.ylkj.system.service.IOmgTrackingLinksService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;
import org.springframework.web.multipart.MultipartFile;

/**
 * omg追踪链接Controller
 *
 * <AUTHOR>
 * @date 2025-06-17
 */
@RestController
@RequestMapping("/system/OmgLinks")
public class OmgTrackingLinksController extends BaseController
{
    @Resource
    private IOmgTrackingLinksService omgTrackingLinksService;

    /**
     * 查询omg追踪链接列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgLinks:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgTrackingLinksQuery omgTrackingLinksQuery)
    {
        OmgTrackingLinks omgTrackingLinks = OmgTrackingLinksQuery.queryToObj(omgTrackingLinksQuery);
        startPage();
        List<OmgTrackingLinks> list = omgTrackingLinksService.selectOmgTrackingLinksList(omgTrackingLinks);
        List<OmgTrackingLinksVo> listVo= list.stream().map(OmgTrackingLinksVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出omg追踪链接列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgLinks:export')")
    @Log(title = "omg追踪链接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgTrackingLinksQuery omgTrackingLinksQuery)
    {
        OmgTrackingLinks omgTrackingLinks = OmgTrackingLinksQuery.queryToObj(omgTrackingLinksQuery);
        List<OmgTrackingLinks> list = omgTrackingLinksService.selectOmgTrackingLinksList(omgTrackingLinks);
        ExcelUtil<OmgTrackingLinks> util = new ExcelUtil<OmgTrackingLinks>(OmgTrackingLinks.class);
        util.exportExcel(response, list, "omg追踪链接数据");
    }

    /**
     * 获取omg追踪链接详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:OmgLinks:query')")
    @GetMapping(value = "/{linkId}")
    public AjaxResult getInfo(@PathVariable("linkId") Long linkId)
    {
        OmgTrackingLinks omgTrackingLinks = omgTrackingLinksService.selectOmgTrackingLinksByLinkId(linkId);
        return success(OmgTrackingLinksVo.objToVo(omgTrackingLinks));
    }

    /**
     * 新增omg追踪链接
     */
    @PreAuthorize("@ss.hasPermi('system:OmgLinks:add')")
    @Log(title = "omg追踪链接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgTrackingLinksInsert omgTrackingLinksInsert)
    {
        OmgTrackingLinks omgTrackingLinks = OmgTrackingLinksInsert.insertToObj(omgTrackingLinksInsert);
        return toAjax(omgTrackingLinksService.insertOmgTrackingLinks(omgTrackingLinks));
    }

    /**
     * 修改omg追踪链接
     */
    @PreAuthorize("@ss.hasPermi('system:OmgLinks:edit')")
    @Log(title = "omg追踪链接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgTrackingLinksEdit omgTrackingLinksEdit)
    {
        OmgTrackingLinks omgTrackingLinks = OmgTrackingLinksEdit.editToObj(omgTrackingLinksEdit);
        return toAjax(omgTrackingLinksService.updateOmgTrackingLinks(omgTrackingLinks));
    }

    /**
     * 删除omg追踪链接
     */
    @PreAuthorize("@ss.hasPermi('system:OmgLinks:remove')")
    @Log(title = "omg追踪链接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{linkIds}")
    public AjaxResult remove(@PathVariable Long[] linkIds)
    {
        return toAjax(omgTrackingLinksService.deleteOmgTrackingLinksByLinkIds(linkIds));
    }

    /**
     * 批量生成追踪链接
     */
    @PreAuthorize("@ss.hasPermi('system:tracking:batch')")
    @Log(title = "批量生成追踪链接", businessType = BusinessType.INSERT)
    @PostMapping("/batch")
    public AjaxResult batchGenerate(@RequestBody BatchGenerateRequest request) {
        return success(omgTrackingLinksService.batchGenerateTrackingLinks(request));
    }

    /**
     * 通过Excel导入批量生成追踪链接
     */
    @PreAuthorize("@ss.hasPermi('system:tracking:import')")
    @Log(title = "Excel导入追踪链接", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file) throws Exception {
        ExcelUtil<OmgTrackingLinksImportVo> util = new ExcelUtil<>(OmgTrackingLinksImportVo.class);
        List<OmgTrackingLinksImportVo> importList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = omgTrackingLinksService.importTrackingLinks(importList, operName);
        return success(message);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException {
        ExcelUtil<OmgTrackingLinksImportVo> util = new ExcelUtil<>(OmgTrackingLinksImportVo.class);
        util.importTemplateExcel(response, "追踪链接数据");
    }

    /**
     * 处理追踪链接重定向
     */
    @GetMapping("/r/{suffix}")
    public void handleRedirect(@PathVariable("suffix") String suffix, HttpServletResponse response, HttpServletRequest request) throws IOException {
        AjaxResult result = omgTrackingLinksService.handleLinkRedirect(suffix);
        // 如果找到链接，同步增加点击量
        if (result.isSuccess()) {
            // 从result中获取linkId
            OmgTrackingLinks link = (OmgTrackingLinks) result.get("link"); // 需要在service中添加link到result
            if (link != null) {
                // 直接同步增加点击量
                omgTrackingLinksService.incrementClickCount(link.getLinkId());

                // 动态构建URL
                String serverName = request.getServerName();  // 获取服务器名称/IP
                int serverPort = 8083;     // 获取服务器端口

                // 构建重定向URL
                String redirectUrl = "http://" + serverName + ":" + serverPort + "/";
                System.out.println("重定向到: " + redirectUrl);

                // 重定向
                response.sendRedirect(redirectUrl);
            }
        }else {
            response.sendRedirect("/error.html");
        }
    }

    /**
     * 处理短链接重定向
     */
    @GetMapping("/s/{shortCode}")
    public void handleShortLinkRedirect(@PathVariable("shortCode") String shortCode, HttpServletResponse response, HttpServletRequest request) throws IOException {
        AjaxResult result = omgTrackingLinksService.handleShortLinkRedirect(shortCode);
        System.out.println("接收到短链接访问: " + shortCode);

        // 根据结果决定操作
        if (result.isSuccess()) {
            // 找到链接，增加点击量
            OmgTrackingLinks link = (OmgTrackingLinks) result.get("link");
            if (link != null) {
                omgTrackingLinksService.incrementClickCount(link.getLinkId());
            }

            // 重定向到前端页面
            String serverName = request.getServerName();
            int serverPort = 8083;
            String redirectUrl = "http://" + serverName + ":" + serverPort + "/";
            System.out.println("重定向到前端: " + redirectUrl);
            response.sendRedirect(redirectUrl);
        } else {
            // 链接不存在或已禁用，重定向到错误页面
            System.out.println("链接无效，重定向到错误页面");
            response.sendRedirect("/error.html"); // 或其他错误页面
        }
    }
}
