package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.OmgPlatformLinks;
import com.ylkj.system.model.vo.omgPlatformLinks.OmgPlatformLinksVo;
import com.ylkj.system.model.dto.omgPlatformLinks.OmgPlatformLinksQuery;
import com.ylkj.system.model.dto.omgPlatformLinks.OmgPlatformLinksInsert;
import com.ylkj.system.model.dto.omgPlatformLinks.OmgPlatformLinksEdit;
import com.ylkj.system.service.IOmgPlatformLinksService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * OMG平台链接Controller
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@RestController
@RequestMapping("/system/OmgPlatformLinks")
public class OmgPlatformLinksController extends BaseController
{
    @Resource
    private IOmgPlatformLinksService omgPlatformLinksService;

    /**
     * 查询OMG平台链接列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgPlatformLinks:list')")
    @GetMapping("/list")
    public TableDataInfo list(OmgPlatformLinksQuery omgPlatformLinksQuery)
    {
        OmgPlatformLinks omgPlatformLinks = OmgPlatformLinksQuery.queryToObj(omgPlatformLinksQuery);
        startPage();
        List<OmgPlatformLinks> list = omgPlatformLinksService.selectOmgPlatformLinksList(omgPlatformLinks);
        List<OmgPlatformLinksVo> listVo= list.stream().map(OmgPlatformLinksVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出OMG平台链接列表
     */
    @PreAuthorize("@ss.hasPermi('system:OmgPlatformLinks:export')")
    @Log(title = "OMG平台链接", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, OmgPlatformLinksQuery omgPlatformLinksQuery)
    {
        OmgPlatformLinks omgPlatformLinks = OmgPlatformLinksQuery.queryToObj(omgPlatformLinksQuery);
        List<OmgPlatformLinks> list = omgPlatformLinksService.selectOmgPlatformLinksList(omgPlatformLinks);
        ExcelUtil<OmgPlatformLinks> util = new ExcelUtil<OmgPlatformLinks>(OmgPlatformLinks.class);
        util.exportExcel(response, list, "OMG平台链接数据");
    }

    /**
     * 获取OMG平台链接详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:OmgPlatformLinks:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        OmgPlatformLinks omgPlatformLinks = omgPlatformLinksService.selectOmgPlatformLinksById(id);
        return success(OmgPlatformLinksVo.objToVo(omgPlatformLinks));
    }

    /**
     * 新增OMG平台链接
     */
    @PreAuthorize("@ss.hasPermi('system:OmgPlatformLinks:add')")
    @Log(title = "OMG平台链接", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OmgPlatformLinksInsert omgPlatformLinksInsert)
    {
        OmgPlatformLinks omgPlatformLinks = OmgPlatformLinksInsert.insertToObj(omgPlatformLinksInsert);
        return toAjax(omgPlatformLinksService.insertOmgPlatformLinks(omgPlatformLinks));
    }

    /**
     * 修改OMG平台链接
     */
    @PreAuthorize("@ss.hasPermi('system:OmgPlatformLinks:edit')")
    @Log(title = "OMG平台链接", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OmgPlatformLinksEdit omgPlatformLinksEdit)
    {
        OmgPlatformLinks omgPlatformLinks = OmgPlatformLinksEdit.editToObj(omgPlatformLinksEdit);
        return toAjax(omgPlatformLinksService.updateOmgPlatformLinks(omgPlatformLinks));
    }

    /**
     * 删除OMG平台链接
     */
    @PreAuthorize("@ss.hasPermi('system:OmgPlatformLinks:remove')")
    @Log(title = "OMG平台链接", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(omgPlatformLinksService.deleteOmgPlatformLinksByIds(ids));
    }
}
