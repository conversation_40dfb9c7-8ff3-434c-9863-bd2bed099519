package com.ylkj.system.service.impl;

import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.model.result.MainImageProcessResult;
import com.ylkj.system.service.IMainImageProcessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

/**
 * 默认主图处理服务实现
 * 当没有其他主图处理服务实现时使用此默认实现
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
@ConditionalOnMissingBean(name = "findQcMainImageProcessService")
public class DefaultMainImageProcessServiceImpl implements IMainImageProcessService {

    /**
     * 处理商品主图
     * 默认实现：暂时不处理，返回失败结果
     * 
     * @param product 商品信息
     * @param mallType 商城类型
     * @return 处理结果
     */
    @Override
    public MainImageProcessResult processProductMainImage(OmgProducts product, String mallType) {
        log.debug("使用默认主图处理服务，SKU: {}, mallType: {}", product.getSku(), mallType);
        
        // 默认实现：暂时不处理主图
        // 这里可以根据需要实现简单的主图处理逻辑
        // 或者返回失败让系统跳过主图更新
        
        return MainImageProcessResult.failure("默认主图处理服务暂不支持主图更新");
    }
}
