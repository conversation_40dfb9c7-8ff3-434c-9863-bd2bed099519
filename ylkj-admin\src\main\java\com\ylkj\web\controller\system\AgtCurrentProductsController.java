package com.ylkj.web.controller.system;

import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.system.model.domain.AgtCurrentProducts;
import com.ylkj.system.model.vo.agtCurrentProducts.AgtCurrentProductsVo;
import com.ylkj.system.model.dto.agtCurrentProducts.AgtCurrentProductsQuery;
import com.ylkj.system.model.dto.agtCurrentProducts.AgtCurrentProductsInsert;
import com.ylkj.system.model.dto.agtCurrentProducts.AgtCurrentProductsEdit;
import com.ylkj.system.service.IAgtCurrentProductsService;
import com.ylkj.common.utils.poi.ExcelUtil;
import com.ylkj.common.core.page.TableDataInfo;

/**
 * 最新商品Controller
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@RestController
@RequestMapping("/system/CurrentProducts")
public class AgtCurrentProductsController extends BaseController
{
    @Resource
    private IAgtCurrentProductsService agtCurrentProductsService;

    /**
     * 查询最新商品列表
     */
    @PreAuthorize("@ss.hasPermi('system:CurrentProducts:list')")
    @GetMapping("/list")
    public TableDataInfo list(AgtCurrentProductsQuery agtCurrentProductsQuery)
    {
        AgtCurrentProducts agtCurrentProducts = AgtCurrentProductsQuery.queryToObj(agtCurrentProductsQuery);
        startPage();
        List<AgtCurrentProducts> list = agtCurrentProductsService.selectAgtCurrentProductsList(agtCurrentProducts);
        List<AgtCurrentProductsVo> listVo= list.stream().map(AgtCurrentProductsVo::objToVo).collect(Collectors.toList());
        TableDataInfo table = getDataTable(list);
        table.setRows(listVo);
        return table;
    }

    /**
     * 导出最新商品列表
     */
    @PreAuthorize("@ss.hasPermi('system:CurrentProducts:export')")
    @Log(title = "最新商品", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgtCurrentProductsQuery agtCurrentProductsQuery)
    {
        AgtCurrentProducts agtCurrentProducts = AgtCurrentProductsQuery.queryToObj(agtCurrentProductsQuery);
        List<AgtCurrentProducts> list = agtCurrentProductsService.selectAgtCurrentProductsList(agtCurrentProducts);
        ExcelUtil<AgtCurrentProducts> util = new ExcelUtil<AgtCurrentProducts>(AgtCurrentProducts.class);
        util.exportExcel(response, list, "最新商品数据");
    }

    /**
     * 获取最新商品详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:CurrentProducts:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        AgtCurrentProducts agtCurrentProducts = agtCurrentProductsService.selectAgtCurrentProductsById(id);
        return success(AgtCurrentProductsVo.objToVo(agtCurrentProducts));
    }

    /**
     * 新增最新商品
     */
    @PreAuthorize("@ss.hasPermi('system:CurrentProducts:add')")
    @Log(title = "最新商品", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AgtCurrentProductsInsert agtCurrentProductsInsert)
    {
        AgtCurrentProducts agtCurrentProducts = AgtCurrentProductsInsert.insertToObj(agtCurrentProductsInsert);
        return toAjax(agtCurrentProductsService.insertAgtCurrentProducts(agtCurrentProducts));
    }

    /**
     * 修改最新商品
     */
    @PreAuthorize("@ss.hasPermi('system:CurrentProducts:edit')")
    @Log(title = "最新商品", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AgtCurrentProductsEdit agtCurrentProductsEdit)
    {
        AgtCurrentProducts agtCurrentProducts = AgtCurrentProductsEdit.editToObj(agtCurrentProductsEdit);
        return toAjax(agtCurrentProductsService.updateAgtCurrentProducts(agtCurrentProducts));
    }

    /**
     * 删除最新商品
     */
    @PreAuthorize("@ss.hasPermi('system:CurrentProducts:remove')")
    @Log(title = "最新商品", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(agtCurrentProductsService.deleteAgtCurrentProductsByIds(ids));
    }

    // 检查今天是否已经存在记录
    @GetMapping("/checkExist")
    public AjaxResult checkExistTodayCurrentProducts() {
        boolean exist = agtCurrentProductsService.checkExistTodayCurrentProducts();
        if (exist) {
            return AjaxResult.success("今天已存在记录");
        } else {
            return AjaxResult.success("今天没有记录");
        }
    }

    // 批量插入最新商品
    @PostMapping("/batchInsert")
    public AjaxResult batchInsertCurrentProducts(@RequestBody List<AgtCurrentProducts> currentProductList) {
        int result = agtCurrentProductsService.batchInsertCurrentProducts(currentProductList);
        if (result > 0) {
            return AjaxResult.success("批量插入成功");
        } else {
            return AjaxResult.error("批量插入失败");
        }
    }

    // 批量更新最新商品
    @PostMapping("/batchUpdate")
    public AjaxResult batchUpdateCurrentProducts(@RequestBody List<AgtCurrentProducts> currentProductList) {
        int result = agtCurrentProductsService.batchUpdateCurrentProducts(currentProductList);
        if (result > 0) {
            return AjaxResult.success("批量更新成功");
        } else {
            return AjaxResult.error("批量更新失败");
        }
    }
}
