package com.ylkj.web.controller.system;

import com.ylkj.common.annotation.Log;
import com.ylkj.common.core.controller.BaseController;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.enums.BusinessType;
import com.ylkj.quartz.domain.SysJob;
import com.ylkj.quartz.service.ISysJobService;
import com.ylkj.system.model.dto.ProductStatusCheckConfig;
import com.ylkj.system.model.dto.ProductStatusCheckResult;
import com.ylkj.system.service.IProductStatusCheckService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 商品状态检查控制器
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Api(tags = "商品状态检查管理")
@Slf4j
@RestController
@RequestMapping("/system/productStatusCheck")
public class ProductStatusCheckController extends BaseController {
    
    @Autowired
    private IProductStatusCheckService productStatusCheckService;
    
    @Autowired
    private ISysJobService jobService;
    
    // 定时任务相关常量
    private static final String JOB_NAME = "商品状态检查任务";
    private static final String JOB_GROUP = "PRODUCT_CHECK";
    private static final String INVOKE_TARGET = "productStatusCheckTask.executeProductStatusCheck";
    
    /**
     * 手动执行商品状态检查
     */
    @ApiOperation("手动执行商品状态检查")
    @PreAuthorize("@ss.hasPermi('system:productStatusCheck:execute')")
    @Log(title = "商品状态检查", businessType = BusinessType.OTHER)
    @PostMapping("/execute")
    public AjaxResult executeCheck() {
        try {
            ProductStatusCheckResult result = productStatusCheckService.batchCheckAndUpdateProductStatus();
            
            if (result.isSuccess()) {
                return AjaxResult.success("商品状态检查完成", result);
            } else {
                return AjaxResult.error("商品状态检查失败: " + result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("手动执行商品状态检查异常", e);
            return AjaxResult.error("执行异常: " + e.getMessage());
        }
    }
    
    /**
     * 检查单个商品状态并更新数据库
     */
    @ApiOperation("检查单个商品状态并更新数据库")
    @PreAuthorize("@ss.hasPermi('system:productStatusCheck:single')")
    @GetMapping("/checkSingle")
    public AjaxResult checkSingleProduct(@RequestParam String sku, @RequestParam String platform) {
        try {
            log.info("开始检查单个商品状态 - SKU: {}, 平台: {}", sku, platform);
            ProductStatusCheckResult result = productStatusCheckService.checkProductStatus(sku, platform);
            log.info("单个商品状态检查结果 - SKU: {}, 平台: {}, 存在: {}, 成功: {}, 错误: {}",
                    sku, platform, result.isProductExists(), result.isSuccess(), result.getErrorMessage());
            return AjaxResult.success("检查完成", result);
        } catch (Exception e) {
            log.error("检查单个商品状态异常 - SKU: {}, 平台: {}", sku, platform, e);
            return AjaxResult.error("检查异常: " + e.getMessage());
        }
    }
    
    /**
     * 分页检查商品状态
     */
    @ApiOperation("分页检查商品状态")
    @PreAuthorize("@ss.hasPermi('system:productStatusCheck:page')")
    @PostMapping("/executePage")
    public AjaxResult executePageCheck(@RequestParam(defaultValue = "100") int pageSize, 
                                      @RequestParam(defaultValue = "1") int pageNum) {
        try {
            ProductStatusCheckResult result = productStatusCheckService.batchCheckProductStatusByPage(pageSize, pageNum);
            
            if (result.isSuccess()) {
                return AjaxResult.success("分页商品状态检查完成", result);
            } else {
                return AjaxResult.error("分页商品状态检查失败: " + result.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("分页执行商品状态检查异常", e);
            return AjaxResult.error("执行异常: " + e.getMessage());
        }
    }
    
    /**
     * 配置定时任务
     */
    @ApiOperation("配置商品状态检查定时任务")
    @PreAuthorize("@ss.hasPermi('system:productStatusCheck:config')")
    @Log(title = "商品状态检查定时任务配置", businessType = BusinessType.UPDATE)
    @PostMapping("/configSchedule")
    public AjaxResult configSchedule(@Validated @RequestBody ProductStatusCheckConfig config) {
        try {
            // 生成Cron表达式
            String cronExpression = config.generateCronExpression();
            
            // 查找是否已存在该任务
            SysJob existingJob = findExistingJob();
            
            if (existingJob != null) {
                // 更新现有任务
                existingJob.setCronExpression(cronExpression);
                existingJob.setStatus(config.getEnabled() ? "0" : "1"); // 0-正常，1-暂停
                existingJob.setRemark(config.getDescription());
                
                int result = jobService.updateJob(existingJob);
                if (result > 0) {
                    return AjaxResult.success("定时任务配置更新成功");
                } else {
                    return AjaxResult.error("定时任务配置更新失败");
                }
            } else {
                // 创建新任务
                SysJob newJob = new SysJob();
                newJob.setJobName(JOB_NAME);
                newJob.setJobGroup(JOB_GROUP);
                newJob.setInvokeTarget(INVOKE_TARGET);
                newJob.setCronExpression(cronExpression);
                newJob.setMisfirePolicy("1"); // 立即触发执行
                newJob.setConcurrent("1"); // 禁止并发执行
                newJob.setStatus(config.getEnabled() ? "0" : "1");
                newJob.setRemark(config.getDescription());
                newJob.setCreateBy(getUsername());
                
                int result = jobService.insertJob(newJob);
                if (result > 0) {
                    return AjaxResult.success("定时任务创建成功");
                } else {
                    return AjaxResult.error("定时任务创建失败");
                }
            }
            
        } catch (Exception e) {
            log.error("配置商品状态检查定时任务异常", e);
            return AjaxResult.error("配置异常: " + e.getMessage());
        }
    }

    /**
     * 获取当前定时任务配置
     */
    @ApiOperation("获取当前定时任务配置")
    @PreAuthorize("@ss.hasPermi('system:productStatusCheck:query')")
    @GetMapping("/getScheduleConfig")
    public AjaxResult getScheduleConfig() {
        try {
            SysJob existingJob = findExistingJob();

            if (existingJob != null) {
                ProductStatusCheckConfig config = parseCronToConfig(existingJob.getCronExpression());
                config.setEnabled("0".equals(existingJob.getStatus()));
                config.setDescription(existingJob.getRemark());

                return AjaxResult.success("获取配置成功", config);
            } else {
                return AjaxResult.success("暂无定时任务配置", null);
            }

        } catch (Exception e) {
            log.error("获取定时任务配置异常", e);
            return AjaxResult.error("获取配置异常: " + e.getMessage());
        }
    }

    /**
     * 启用/禁用定时任务
     */
    @ApiOperation("启用/禁用定时任务")
    @PreAuthorize("@ss.hasPermi('system:productStatusCheck:toggle')")
    @Log(title = "商品状态检查定时任务开关", businessType = BusinessType.UPDATE)
    @PostMapping("/toggleSchedule")
    public AjaxResult toggleSchedule(@RequestParam Boolean enabled) {
        try {
            SysJob existingJob = findExistingJob();

            if (existingJob == null) {
                return AjaxResult.error("定时任务不存在，请先配置定时任务");
            }

            String newStatus = enabled ? "0" : "1"; // 0-正常，1-暂停
            existingJob.setStatus(newStatus);

            int result = jobService.updateJob(existingJob);
            if (result > 0) {
                String action = enabled ? "启用" : "禁用";
                return AjaxResult.success("定时任务" + action + "成功");
            } else {
                return AjaxResult.error("定时任务状态更新失败");
            }

        } catch (Exception e) {
            log.error("切换定时任务状态异常", e);
            return AjaxResult.error("操作异常: " + e.getMessage());
        }
    }

    /**
     * 查看数据库商品统计信息
     */
    @ApiOperation("查看数据库商品统计信息")
    @PreAuthorize("@ss.hasPermi('system:productStatusCheck:query')")
    @GetMapping("/getProductStats")
    public AjaxResult getProductStats() {
        try {
            // 调用统计方法
            productStatusCheckService.logProductStatistics();
            return AjaxResult.success("统计信息获取成功，请查看日志");
        } catch (Exception e) {
            log.error("获取商品统计信息异常", e);
            return AjaxResult.error("获取统计信息异常: " + e.getMessage());
        }
    }

    /**
     * 删除定时任务
     */
    @ApiOperation("删除定时任务")
    @PreAuthorize("@ss.hasPermi('system:productStatusCheck:remove')")
    @Log(title = "商品状态检查定时任务删除", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeSchedule")
    public AjaxResult removeSchedule() {
        try {
            SysJob existingJob = findExistingJob();

            if (existingJob == null) {
                return AjaxResult.error("定时任务不存在");
            }

            int result = jobService.deleteJob(existingJob);
            if (result > 0) {
                return AjaxResult.success("定时任务删除成功");
            } else {
                return AjaxResult.error("定时任务删除失败");
            }

        } catch (Exception e) {
            log.error("删除定时任务异常", e);
            return AjaxResult.error("删除异常: " + e.getMessage());
        }
    }

    /**
     * 查找现有的商品状态检查任务
     */
    private SysJob findExistingJob() {
        try {
            return jobService.selectJobByName(JOB_NAME, JOB_GROUP);
        } catch (Exception e) {
            log.warn("查找现有任务异常", e);
            return null;
        }
    }

    /**
     * 解析Cron表达式为配置对象
     * Cron格式: 秒 分 时 日 月 周
     */
    private ProductStatusCheckConfig parseCronToConfig(String cronExpression) {
        ProductStatusCheckConfig config = new ProductStatusCheckConfig();

        try {
            String[] parts = cronExpression.split(" ");
            if (parts.length >= 4) {
                config.setMinute(Integer.parseInt(parts[1]));
                config.setHour(Integer.parseInt(parts[2]));
                config.setDayOfMonth(Integer.parseInt(parts[3]));
            }
        } catch (Exception e) {
            log.warn("解析Cron表达式异常: {}", cronExpression, e);
            // 设置默认值
            config.setMinute(0);
            config.setHour(2);
            config.setDayOfMonth(1);
        }

        return config;
    }
}
